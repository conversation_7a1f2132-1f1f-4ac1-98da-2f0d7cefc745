namespace DataAICrawler {
    partial class CrawlerData
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent() {
            webView = new Microsoft.Web.WebView2.WinForms.WebView2();
            txtURL = new TextBox();
            btnGo = new Button();
            btnGetAllGame = new Button();
            label2 = new Label();
            rtbResult = new RichTextBox();
            btnClear = new Button();
            btnInsert = new Button();
            btnSave = new Button();
            btnLoad = new Button();
            cmbUrl = new ComboBox();
            ((System.ComponentModel.ISupportInitialize)webView).BeginInit();
            SuspendLayout();
            // 
            // webView
            // 
            webView.AccessibleName = "webView";
            webView.AllowExternalDrop = true;
            webView.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            webView.CreationProperties = null;
            webView.DefaultBackgroundColor = Color.White;
            webView.Location = new Point(-1, 69);
            webView.Margin = new Padding(4, 3, 4, 3);
            webView.Name = "webView";
            webView.Size = new Size(977, 633);
            webView.TabIndex = 0;
            webView.ZoomFactor = 1D;
            // 
            // txtURL
            // 
            txtURL.AccessibleName = "addressBar";
            txtURL.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            txtURL.Location = new Point(68, 2);
            txtURL.Margin = new Padding(4, 3, 4, 3);
            txtURL.Name = "txtURL";
            txtURL.Size = new Size(698, 23);
            txtURL.TabIndex = 1;
            txtURL.Text = "https://www.appbrain.com/stats/google-play-rankings/top_grossing/puzzle/us#";
            txtURL.TextChanged += TxtUrlTextChanged;
            // 
            // btnGo
            // 
            btnGo.AccessibleName = "goButton";
            btnGo.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnGo.Location = new Point(903, 2);
            btnGo.Margin = new Padding(4, 3, 4, 3);
            btnGo.Name = "btnGo";
            btnGo.Size = new Size(61, 24);
            btnGo.TabIndex = 2;
            btnGo.Text = "Go!";
            btnGo.UseVisualStyleBackColor = true;
            btnGo.Click += btnGo_Click;
            // 
            // btnGetAllGame
            // 
            btnGetAllGame.AccessibleName = "goButton";
            btnGetAllGame.Location = new Point(8, 31);
            btnGetAllGame.Margin = new Padding(4, 3, 4, 3);
            btnGetAllGame.Name = "btnGetAllGame";
            btnGetAllGame.Size = new Size(86, 24);
            btnGetAllGame.TabIndex = 3;
            btnGetAllGame.Text = "Get all game";
            btnGetAllGame.UseVisualStyleBackColor = true;
            btnGetAllGame.Click += btnGetAllGame_Click;
            // 
            // label2
            // 
            label2.AutoSize = true;
            label2.Location = new Point(8, 7);
            label2.Name = "label2";
            label2.Size = new Size(28, 15);
            label2.TabIndex = 6;
            label2.Text = "URL";
            // 
            // rtbResult
            // 
            rtbResult.Anchor = AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            rtbResult.Location = new Point(-1, 708);
            rtbResult.Name = "rtbResult";
            rtbResult.Size = new Size(872, 163);
            rtbResult.TabIndex = 7;
            rtbResult.Text = "";
            // 
            // btnClear
            // 
            btnClear.AccessibleName = "goButton";
            btnClear.Anchor = AnchorStyles.Bottom | AnchorStyles.Right;
            btnClear.Location = new Point(878, 708);
            btnClear.Margin = new Padding(4, 3, 4, 3);
            btnClear.Name = "btnClear";
            btnClear.Size = new Size(86, 24);
            btnClear.TabIndex = 8;
            btnClear.Text = "Clear";
            btnClear.UseVisualStyleBackColor = true;
            btnClear.Click += btnClear_Click;
            // 
            // btnInsert
            // 
            btnInsert.AccessibleName = "goButton";
            btnInsert.Location = new Point(102, 31);
            btnInsert.Margin = new Padding(4, 3, 4, 3);
            btnInsert.Name = "btnInsert";
            btnInsert.Size = new Size(86, 24);
            btnInsert.TabIndex = 9;
            btnInsert.Text = "Insert game";
            btnInsert.UseVisualStyleBackColor = true;
            btnInsert.Click += btnInsert_Click;
            // 
            // btnSave
            // 
            btnSave.AccessibleName = "goButton";
            btnSave.Anchor = AnchorStyles.Bottom | AnchorStyles.Right;
            btnSave.Location = new Point(878, 738);
            btnSave.Margin = new Padding(4, 3, 4, 3);
            btnSave.Name = "btnSave";
            btnSave.Size = new Size(86, 24);
            btnSave.TabIndex = 10;
            btnSave.Text = "Save";
            btnSave.UseVisualStyleBackColor = true;
            btnSave.Click += btnSave_Click;
            // 
            // btnLoad
            // 
            btnLoad.AccessibleName = "goButton";
            btnLoad.Anchor = AnchorStyles.Bottom | AnchorStyles.Right;
            btnLoad.Location = new Point(878, 768);
            btnLoad.Margin = new Padding(4, 3, 4, 3);
            btnLoad.Name = "btnLoad";
            btnLoad.Size = new Size(86, 24);
            btnLoad.TabIndex = 11;
            btnLoad.Text = "Load";
            btnLoad.UseVisualStyleBackColor = true;
            btnLoad.Click += btnLoad_Click;
            // 
            // cmbUrl
            // 
            cmbUrl.FormattingEnabled = true;
            cmbUrl.Location = new Point(773, 3);
            cmbUrl.Name = "cmbUrl";
            cmbUrl.Size = new Size(121, 23);
            cmbUrl.TabIndex = 12;
            cmbUrl.SelectedIndexChanged += cmbUrl_SelectedIndexChanged;
            // 
            // CrawlerData
            // 
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(977, 872);
            Controls.Add(cmbUrl);
            Controls.Add(btnLoad);
            Controls.Add(btnSave);
            Controls.Add(btnInsert);
            Controls.Add(btnClear);
            Controls.Add(rtbResult);
            Controls.Add(label2);
            Controls.Add(btnGetAllGame);
            Controls.Add(btnGo);
            Controls.Add(txtURL);
            Controls.Add(webView);
            Margin = new Padding(4, 3, 4, 3);
            Name = "CrawlerData";
            Text = "Crawler Data";
            ((System.ComponentModel.ISupportInitialize)webView).EndInit();
            ResumeLayout(false);
            PerformLayout();
        }

        #endregion

        private Microsoft.Web.WebView2.WinForms.WebView2 webView;
        private System.Windows.Forms.TextBox txtURL;
        private System.Windows.Forms.Button btnGo;
        private Button btnGetAllGame;
        private Label label2;
        private RichTextBox rtbResult;
        private Button btnClear;
        private Button btnInsert;
        private Button btnSave;
        private Button btnLoad;
        private ComboBox cmbUrl;
    }
}

