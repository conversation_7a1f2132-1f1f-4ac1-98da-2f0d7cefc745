using System;
using System.Configuration;
using System.Diagnostics;
using System.Security.Cryptography;
using System.Security.Policy;
using System.Text;
using System.Text.RegularExpressions;
using System.Web;
using DataAICrawler.Scripts;
using HtmlAgilityPack;
using Microsoft.Data.Sqlite;
using Microsoft.Web.WebView2.Core;

namespace DataAICrawler {
    public partial class CrawlerData : Form {
        public CrawlerData() {
            InitializeComponent();
            InitializeAsync();
            InitializeUI();
        }

        #region Buttons

        private async void btnGetAllGame_Click(object sender, EventArgs e) {
            try {
                List<GameInfo>? products;
                if (txtURL.Text.Contains(GameInfo.Type.appbrain.ToString())) {
                    products = await GetDataFromAppBrain();
                } else if (txtURL.Text.Contains(GameInfo.Type.sensortower.ToString())) {
                    products = await GetDataFromSensortower();
                } else {
                    AddLog("Invalid URL: " + txtURL.Text);
                    return;
                }

                if (products != null) {
                    for (int i = 0; i < products.Count; i++) {
                        GameInfo product = products[i];
                        InsertDB(product);
                    }
                }

                AddLog("Finished running!");

            } catch (Exception ex) {
                AddLog(ex.Message);
            }
        }

        private async void btnGo_Click(object sender, EventArgs e) {
            string link = txtURL.Text.Trim();
            await OpenURL(link);
        }

        private async void btnInsert_Click(object sender, EventArgs e) {
            GameInfo? gameInfo = await GetGameInfo();
            if (gameInfo != null) {
                InsertGameInfo(gameInfo);
                AddLog(gameInfo.GetResult());
                AddLog("Data inserted successfully");
            } else {
                AddLog("No data to insert " + txtURL.Text);
            }
        }

        private void btnClear_Click(object sender, EventArgs e) {
            rtbResult.Clear();
        }

        private void btnSave_Click(object sender, EventArgs e) {

        }

        private void btnLoad_Click(object sender, EventArgs e) {

        }

        #endregion

        async void InitializeAsync() {
            webView.NavigationStarting += webView_NavigationStarting;
            webView.NavigationCompleted += NavigationCompleted;
            await webView.EnsureCoreWebView2Async(null);
            webView.CoreWebView2.WebMessageReceived += UpdateAddressBar;

            //await webView.CoreWebView2.AddScriptToExecuteOnDocumentCreatedAsync("window.chrome.webview.postMessage(window.document.URL);");
            //await webView.CoreWebView2.AddScriptToExecuteOnDocumentCreatedAsync("window.chrome.webview.addEventListener(\'message\', event => alert(event.data));");
        }

        private void InitializeUI() {
            foreach (KeyValuePair<string, string> pair in dicUrl) {
                cmbUrl.Items.Add(pair.Key);
            }
        }

        private Dictionary<string, string> dicUrl = new() {
            { "Top Puzzle", "https://www.appbrain.com/stats/google-play-rankings/top_grossing/puzzle/us#" },
            { "ABI Games Studio", "https://app.sensortower.com/android/publisher/publisher/ABI%2BGames%2BStudio" }
        };

        private void cmbUrl_SelectedIndexChanged(object sender, EventArgs e) {
            if (dicUrl.TryGetValue(cmbUrl.Text, out string url)) {
                txtURL.Text = url;
            }
        }

        private void webView_NavigationStarting(object sender, CoreWebView2NavigationStartingEventArgs args) {
            String uri = args.Uri;
            txtURL.Text = uri;

            // if (!uri.StartsWith("https://")) {
            //     webView.CoreWebView2.ExecuteScriptAsync($"alert('{uri} is not safe, try an https link')");
            //     args.Cancel = true;
            // }
        }

        void UpdateAddressBar(object sender, CoreWebView2WebMessageReceivedEventArgs args) {
            String uri = args.TryGetWebMessageAsString();
            txtURL.Text = uri;
            webView.CoreWebView2.PostWebMessageAsString(uri);
        }

        private async void NavigationCompleted(object? sender, CoreWebView2NavigationCompletedEventArgs e) {
            _isLoadDone = true;
        }

        bool _isLoadDone;

        string dbName = "Game_Android";

        private async Task OpenURL(string url) {
            if (webView != null && webView.CoreWebView2 != null) {
                _isLoadDone = false;
                webView.CoreWebView2.Navigate(url);
                AddLog("open URL " + url);

                while (!_isLoadDone) {
                    await Task.Delay(1000);
                }
            }
        }

        private bool InsertDB(GameInfo product) {
            return InsertDB(product.Package, product.Name, product.PublishName, product.Downloads,
                product.DailyDownloads);
        }

        private bool InsertDB(string package, string name, string publishName, long downloads, long dailyDownloads) {
            string sql = $@"INSERT INTO {dbName}(package, name, publishName,downloads, dailyDownloads)
                            VALUES(""{package}"",""{name}"",""{publishName}"",""{downloads}"",""{dailyDownloads}"");";

            try {
                SqliteCommand insertCommand = Home.GetConnection().CreateCommand();
                insertCommand.CommandText = sql;
                object? v = insertCommand.ExecuteNonQuery();

                AddLog($"Insert {package} => {name}");
                return true;

            } catch (Exception ex) {
                string err = ex.Message;
                if (err.Contains("UNIQUE constraint failed")) {
                    //AddLog("UNIQUE constraint failed => " + package);
                } else {
                    AddLog($"{err} => {sql}");
                }

                return false;
            }
        }

        private async Task<List<GameInfo>?> GetDataFromAppBrain() {
            try {
                if (!txtURL.Text.Contains("appbrain.com/stats")) {
                    MessageBox.Show("Invalid URL: " + txtURL.Text);
                    return null;
                }

                //https://www.appbrain.com/stats/google-play-rankings/top_grossing/puzzle/us#
                string script = "document.getElementById(\"rankings-table\").innerHTML;";
                string scriptResult = await ExecuteScriptAsync(script);

                if (scriptResult == null) {
                    return null;
                }

                //string path = "E:\\Projects\\VisualStudioProjects\\DataAI_Crawler\\Data\\scriptResult.html";
                //File.WriteAllText(path, scriptResult);
                //string scriptResult = File.ReadAllText(path);

                HtmlAgilityPack.HtmlDocument document = new();
                document.LoadHtml(scriptResult);

                HtmlNode? htmlNode = document.DocumentNode.GetChildByTag("tbody");
                List<GameInfo> products = new List<GameInfo>();

                foreach (HtmlNode node in htmlNode.ChildNodes) {
                    if (node.Name == "tr") {
                        GameInfo p = new GameInfo(node, GameInfo.Type.appbrain);
                        products.Add(p);
                    }
                }

                return products;

            } catch (Exception ex) {
                AddLog($"{ex.Message}");
                return null;
            }
        }

        private async Task<string?> ExecuteScriptAsync(string script) {
            string scriptResult = await webView.ExecuteScriptAsync(script);

            if (scriptResult == "null") {
                return null;
            }

            scriptResult = scriptResult.Remove(0, 1).Remove(scriptResult.Length - 2, 1).Trim(); // " first " end
            scriptResult = Regex.Unescape(scriptResult);
            return scriptResult;
        }

        private void AddLog(string s) {
            rtbResult.Text = s + "\n" + rtbResult.Text;
        }

        private void ClearLog(int index) {
            File.WriteAllText(index + ".txt", rtbResult.Text);
            rtbResult.Clear();
        }

        private void TxtUrlTextChanged(object sender, EventArgs e) {

        }

        public async Task<GameInfo?> GetGameInfo(string packageName) {
            string link = "https://www.appbrain.com/app/" + packageName;
            txtURL.Text = link;
            await OpenURL(link);
            GameInfo? gameInfo = await GetGameInfo();
            
            if (gameInfo != null) {
                if (gameInfo.Package == null) {
                    gameInfo.Package = packageName;
                }

                AddLog($"AvailableDate: {gameInfo.AvailableDate}");
                AddLog($"LastUpdateDate: {gameInfo.LastUpdateDate}");
                AddLog($"TotalDownloads: {gameInfo.Downloads}");
                AddLog($"DailyDownloads: {gameInfo.DailyDownloads}");
            } else {
                AddLog("No data");
                return null;
            }

            return gameInfo;
        }

        private async Task<GameInfo?> GetGameInfo() {
            try {
                if (!txtURL.Text.Contains("appbrain.com/app/")) {
                    MessageBox.Show("Invalid URL: " + txtURL.Text);
                    return null;
                }

                string script = "document.getElementById(\"main_content\").innerHTML;";
                string scriptResult = await ExecuteScriptAsync(script);

                if (scriptResult == null) {
                    return new GameInfo();
                }

                //File.WriteAllText( "test_data.html", scriptResult);
                //string scriptResult = File.ReadAllText("test_data.html");

                HtmlAgilityPack.HtmlDocument document = new();
                document.LoadHtml(scriptResult);

                List<HtmlNode> htmlNodes = document.DocumentNode.GetChildsByClass("row");
                if (htmlNodes.Count > 0) {
                    GameInfo? gameInfo = new(htmlNodes);
                    return gameInfo;
                } else {
                    MessageBox.Show("Cannot find class ROW in the HTML from the website " + txtURL.Text);
                    return null;
                }

            } catch (Exception ex) {
                AddLog($"[GetGameInfo] {ex.Message}");
                return null;
            }
        }

        private void InsertGameInfo(GameInfo gameInfo) {
            string query =
                "INSERT INTO Game_Android (AvailableDate, LastUpdateDate, downloads, dailyDownloads, package, name, publishName) " +
                "VALUES (@AvailableDate, @LastUpdateDate, @downloads, @dailyDownloads, @package, @name, @publishName);";

            try {
                using (SqliteCommand cmd = new SqliteCommand(query, Home.GetConnection())) {
                    cmd.Parameters.AddWithValue("@AvailableDate", gameInfo.AvailableDate);
                    cmd.Parameters.AddWithValue("@LastUpdateDate", gameInfo.LastUpdateDate);
                    cmd.Parameters.AddWithValue("@downloads", gameInfo.Downloads);
                    cmd.Parameters.AddWithValue("@dailyDownloads", gameInfo.DailyDownloads);
                    cmd.Parameters.AddWithValue("@package", gameInfo.Package);
                    cmd.Parameters.AddWithValue("@name", gameInfo.Name);
                    cmd.Parameters.AddWithValue("@publishName", gameInfo.PublishName);
                    cmd.ExecuteNonQuery();
                }

            } catch (Exception ex) {
                string err = ex.Message;
                if (err.Contains("UNIQUE constraint failed")) {
                    AddLog($"UNIQUE constraint failed => {gameInfo.Name} => {gameInfo.Package}");
                } else {
                    AddLog($"{err} => {query}");
                }
            }
        }

        public bool IsInited() {
            return webView != null && webView.CoreWebView2 != null;
        }

        private async Task<List<GameInfo>?> GetDataFromSensortower() {
            try {
                //https://app.sensortower.com/android/publisher/publisher/ABI%2BGames%2BStudio
                string script = "document.getElementById(\"publisher-profile-table\").innerHTML;";
                string scriptResult = await ExecuteScriptAsync(script);

                if (scriptResult == null) {
                    return null;
                }

                //File.WriteAllText("test_data.html", scriptResult);
                //string scriptResult = File.ReadAllText("test_data.html");

                HtmlAgilityPack.HtmlDocument document = new();
                document.LoadHtml(scriptResult);

                HtmlNode tbody = document.DocumentNode.GetChildByTag("tbody", false);
                List<HtmlNode> lsTr = tbody.GetChildsByTag("tr", false);
                List<GameInfo> products = new();

                string s = txtURL.Text.Split("/")[6];
                string publishName = HttpUtility.UrlDecode(s).Replace("+", " ");

                foreach (HtmlNode htmlNode in lsTr) {
                    GameInfo p = new GameInfo(htmlNode, GameInfo.Type.sensortower);
                    p.PublishName = publishName;
                    products.Add(p);
                }

                return products;

            } catch (Exception ex) {
                AddLog($"{ex.Message}");
                return null;
            }
        }
    }
}