using ApkUtils;
using DataAICrawler.Scripts;
using Microsoft.Data.Sqlite;
using Microsoft.VisualBasic.Logging;
using System.Data;
using System.Globalization;
using System.Reflection;
using System.Text.RegularExpressions;
using System.Windows.Forms;
using static System.Runtime.InteropServices.JavaScript.JSType;
using String = System.String;

namespace DataAICrawler {
    public partial class ProcessData : Form {
        private ApkUtilsForm   _apkUtilsForm;
        private List<GameInfo> _gameInfos;

        public ProcessData() {
            InitializeComponent();
        }

        #region Button Action

        private void btnLoad_Click(object sender, EventArgs e) {
            _gameInfos = LoadGames();
            dgvGameInfo.DataSource = _gameInfos;
            txtTotal.Text = _gameInfos.Count.ToString();
        }

        private void btnApkUtils_Click(object sender, EventArgs e) {
            if (_apkUtilsForm == null || _apkUtilsForm.IsDisposed) {
                _apkUtilsForm = new ApkUtilsForm();
                _apkUtilsForm.Show();
            } else if (_apkUtilsForm.Visible) {
                _apkUtilsForm.Activate();
            } else {
                _apkUtilsForm.Show();
            }

            _apkUtilsForm.SetPackage(txtPackageName.Text);
        }

        private void btnSetChecked_Click(object sender, EventArgs e) {
            UpdateRowIsChecked();
        }

        private void btnSetFavorite_Click(object sender, EventArgs e) {
            UpdateRowIsChecked(true);
        }

        private readonly Dictionary<string, string> _dicSearch = new();

        private void btnSearch_Click(object sender, EventArgs e) {
            string key = txtSearch.Text.Trim();
            string availableDate = txtAvailableDate.Text.Trim();
            string lastUpdateDate = txtLastUpdateDate.Text.Trim();

            _dicSearch.Clear();
            _dicSearch["key"] = key;
            if (!string.IsNullOrEmpty(availableDate)) {
                _dicSearch["AvailableDate"] = availableDate;
            }

            if (!string.IsNullOrEmpty(lastUpdateDate)) {
                _dicSearch["LastUpdateDate"] = lastUpdateDate;
            }

            List<GameInfo> data = Search(_gameInfos, _dicSearch);
            dgvGameInfo.DataSource = data;
            txtTotal.Text = data.Count.ToString();
        }

        private void btnClear_Click(object? sender, EventArgs? e) {
            txtSearch.Clear();
            txtAvailableDate.Clear();
            txtLastUpdateDate.Clear();

            if (_gameInfos != null) {
                dgvGameInfo.DataSource = _gameInfos;
                txtTotal.Text = _gameInfos.Count.ToString();
            }
        }

        private async void btnUpdateRow_Click(object sender, EventArgs e) {
            await Home.instance.InitWebView();
            string packageName = txtPackageName.Text;
            GameInfo? gameInfo = await Home.instance.crawlerData.GetGameInfo(packageName);
            if (gameInfo != null) {
                SetData(packageName, gameInfo);
            }
        }

        private async void btnOpenWebView_ClickAsync(object sender, EventArgs e) {
            await Home.instance.InitWebView(true);
        }

        private void btnStartUpdate_Click(object sender, EventArgs e) {
            isRunStartUpdate = !isRunStartUpdate;
            if (isRunStartUpdate) {
                btnStartUpdate.Text = "Stop Update";
                StartUpdate();
            } else {
                btnStartUpdate.Text = "Start Update";
            }
        }

        #endregion

        private List<GameInfo> LoadGames() {
            List<GameInfo> products = new List<GameInfo>();

            int isChecked = cbIsChecked.Checked ? 1 : 0;
            int isFavorite = cbIsFavorite.Checked ? 1 : 0;

            string query =
                "SELECT package, name, publishName, downloads, dailyDownloads, AvailableDate, LastUpdateDate " +
                "FROM Game_Android where isChecked = " + isChecked + " and isFavorite = " + isFavorite;
            using (SqliteCommand cmd = new(query, Home.GetConnection())) {
                using (SqliteDataReader reader = cmd.ExecuteReader()) {
                    while (reader.Read()) {
                        string package = reader["package"].ToString();
                        string name = reader["name"].ToString();
                        string publishName = reader["publishName"].ToString();
                        //string urlIcon = reader["urlIcon"].ToString();
                        long downloads = (long) reader["downloads"];
                        long dailyDownloads = (long) reader["dailyDownloads"];
                        string AvailableDate = reader["AvailableDate"].ToString();
                        string LastUpdateDate = reader["LastUpdateDate"].ToString();

                        //Console.WriteLine($"Package: {package}, Name: {name}, Publish Name: {publishName}, URL Icon: {urlIcon}");

                        GameInfo p = new GameInfo(package, name, publishName, downloads, dailyDownloads, AvailableDate,
                            LastUpdateDate);
                        products.Add(p);
                    }
                }
            }

            return products;
        }

        private DataGridViewRow _preRow;

        private void dgvGameInfo_SelectionChanged(object sender, EventArgs e) {
            if (dgvGameInfo.CurrentRow == null || _preRow == dgvGameInfo.CurrentRow) {
                return;
            }

            GetInfoFromCurrentRow();
        }

        private void GetInfoFromCurrentRow() {
            _preRow = dgvGameInfo.CurrentRow;

            // Lấy dữ liệu của dòng hiện tại
            DataGridViewRow currentRow = dgvGameInfo.CurrentRow;

            // Lấy các giá trị từ các cột
            if (currentRow == null) {
                return;
            }

            GameInfo selectedGame = new GameInfo(currentRow);
            ShowCurrentRow(selectedGame);
        }

        private void ShowCurrentRow(GameInfo selectedGame) {
            txtName.Text = selectedGame.Name;
            txtPackageName.Text = selectedGame.Package;
            txtPublisher.Text = selectedGame.PublishName;

            //string imageUrl = selectedGame.UrlIcon;
            //LoadImageFromUrl(imageUrl);

            if (_apkUtilsForm != null) {
                _apkUtilsForm.SetPackage(txtPackageName.Text);
            }
        }

        // private async void LoadImageFromUrl(string imageUrl) {
        //     try {
        //         pbIcon.Image = null;
        //
        //         // Tạo HttpClient để tải hình ảnh
        //         using (HttpClient client = new HttpClient()) {
        //             // Tải dữ liệu ảnh từ URL
        //             byte[] imageBytes = await client.GetByteArrayAsync(imageUrl);
        //
        //             // Giải mã và hiển thị ảnh
        //             Bitmap webpImage = WebP.LoadWebP(imageBytes);
        //             pbIcon.Image = webpImage;
        //         }
        //
        //     } catch (Exception ex) {
        //         //MessageBox.Show($"Lỗi khi tải hình ảnh: {ex.Message}");
        //     }
        // }

        private List<GameInfo> Search(List<GameInfo> gameInfos, Dictionary<string, string> dicSearch) {
            List<GameInfo> result = new();
            string key = dicSearch["key"].ToLower();

            foreach (GameInfo gameInfo in gameInfos) {
                PropertyInfo[] propertyInfos = typeof(GameInfo).GetProperties();

                bool hasKey = HasKey(propertyInfos, key, gameInfo);
                if (!hasKey) {
                    continue;
                }

                bool newMethod = false;
                foreach (KeyValuePair<string, string> search in dicSearch) {
                    newMethod = HasPropertyKey(propertyInfos, search, gameInfo);
                    if (!newMethod) {
                        break;
                    }
                }

                if (!newMethod) {
                    continue;
                }

                result.Add(gameInfo);
            }

            return result;
        }

        private static bool HasPropertyKey(PropertyInfo[] propertyInfos, KeyValuePair<string, string> dicSearch,
                                           GameInfo gameInfo) {
            foreach (PropertyInfo property in propertyInfos) {
                object? value = property.GetValue(gameInfo);
                if (value == null) {
                    continue;
                }

                if (property.Name == dicSearch.Key) {
                    string searchValue = dicSearch.Value;
                    string propertyValue = value.ToString()!.ToLower();
                    if (!string.IsNullOrEmpty(searchValue) && propertyValue.Contains(searchValue)) {
                        return true;
                    }

                    return false;
                }

            }

            return true;
        }

        private bool HasKey(PropertyInfo[] propertyInfos, string key, GameInfo gameInfo) {
            if (string.IsNullOrEmpty(key)) {
                return true;
            }

            foreach (PropertyInfo property in propertyInfos) {
                object? value = property.GetValue(gameInfo);
                if (value == null) {
                    continue;
                }

                string propertyValue = value.ToString()!.ToLower();
                if (propertyValue.Contains(key)) {
                    return true;
                }
            }

            return false;
        }

        private void SetData(string packageName, GameInfo gameInfo) {
            //Update DB
            string query = "UPDATE Game_Android SET " + //
                           "AvailableDate = @AvailableDate, " + //
                           "LastUpdateDate = @LastUpdateDate, " + //
                           "downloads = @downloads, " + //
                           "dailyDownloads = @dailyDownloads " + "WHERE package = @package";

            using (SqliteCommand cmd = new SqliteCommand(query, Home.GetConnection())) {
                cmd.Parameters.AddWithValue("@AvailableDate", gameInfo.AvailableDate);
                cmd.Parameters.AddWithValue("@LastUpdateDate", gameInfo.LastUpdateDate);
                cmd.Parameters.AddWithValue("@downloads", gameInfo.Downloads);
                cmd.Parameters.AddWithValue("@dailyDownloads", gameInfo.DailyDownloads);
                cmd.Parameters.AddWithValue("@package", packageName);
                cmd.ExecuteNonQuery();
            }

            //Update DataGridView
            if (dgvGameInfo.CurrentRow != null) {
                DataGridViewRow currentRow = dgvGameInfo.CurrentRow;
                GameInfo dataBoundItem = (GameInfo) currentRow.DataBoundItem;

                if (dataBoundItem.Package == packageName) {
                    dataBoundItem.AvailableDate = gameInfo.AvailableDate;
                    dataBoundItem.LastUpdateDate = gameInfo.LastUpdateDate;
                    dataBoundItem.Downloads = gameInfo.Downloads;
                    dataBoundItem.DailyDownloads = gameInfo.DailyDownloads;

                    dgvGameInfo.Refresh();
                }
            }
        }

        private void dgvGameInfo_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e) {
            if (e.Value == null) {
                return;
            }

            // Kiểm tra nếu cột là cột cần định dạng
            string columnName = dgvGameInfo.Columns[e.ColumnIndex].Name;
            if (columnName == "Downloads" || columnName == "DailyDownloads") {
                // Định dạng số nguyên với dấu phẩy
                e.Value = string.Format("{0:N0}", e.Value);
                e.CellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
                e.FormattingApplied = true;
            }
        }

        bool isRunStartUpdate = false;

        private async void StartUpdate() {
            await Home.instance.InitWebView();

            while (isRunStartUpdate) {
                DataGridViewRow? currentRow = SelectNextRow(dgvGameInfo);
                if (currentRow == null) {
                    btnStartUpdate_Click(null, null);
                    break;
                }

                string packageName = txtPackageName.Text;
                GameInfo? gameInfo = await Home.instance.crawlerData.GetGameInfo(packageName);
                if (gameInfo != null) {
                    if (!string.IsNullOrEmpty(gameInfo.Package)) {
                        SetData(packageName, gameInfo);
                    }
                } else {
                    btnStartUpdate_Click(null, null);
                    break;
                }

                await Task.Delay(1000);
            }
        }

        private DataGridViewRow? SelectNextRow(DataGridView dataGridView) {
            if (dataGridView.CurrentRow == null) {
                return null;
            }

            int currentIndex = dataGridView.CurrentRow.Index;
            int nextIndex = currentIndex + 1;

            // Kiểm tra nếu còn hàng tiếp theo
            if (nextIndex < dataGridView.Rows.Count) {
                dataGridView.ClearSelection(); // Bỏ chọn hàng hiện tại
                dataGridView.Rows[nextIndex].Selected = true; // Chọn hàng tiếp theo
                dataGridView.CurrentCell = dataGridView.Rows[nextIndex].Cells[0]; // Đặt con trỏ vào hàng tiếp theo

                dgvGameInfo_SelectionChanged(null, null);
                return dataGridView.CurrentRow;
            } else {
                return null;
            }
        }

        private void dgvGameInfo_ColumnHeaderMouseClick(object sender, DataGridViewCellMouseEventArgs e) {
            string columnName = dgvGameInfo.Columns[e.ColumnIndex].Name;
            PropertyInfo? property = typeof(GameInfo).GetProperty(columnName);
            if (property == null) {
                return;
            }

            bool ascending = dgvGameInfo.Tag?.ToString() != "ASC";

            List<GameInfo> gameInfos = (List<GameInfo>) dgvGameInfo.DataSource;
            List<GameInfo> data = gameInfos.OrderBy(d => {
                if (columnName != "AvailableDate" && columnName != "LastUpdateDate") {
                    return property.GetValue(d);
                }

                // Sort date
                if (DateTime.TryParseExact(property?.GetValue(d)?.ToString() ?? string.Empty, "dd/MM/yyyy",
                        CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime date)) {
                    return date;
                } else {
                    return DateTime.MinValue;
                }

            }).ToList();
            if (!ascending) {
                data.Reverse();
            }

            dgvGameInfo.DataSource = data;
            dgvGameInfo.Tag = ascending ? "ASC" : "DESC";
        }

        string pattern = @"Index (\d+) does not have a value\.";

        private void dgvGameInfo_DataError(object sender, DataGridViewDataErrorEventArgs e) {
            //Index 88 does not have a value.
            string exceptionMessage = e.Exception.Message;
            Match match = Regex.Match(exceptionMessage, pattern);
            if (!match.Success) {
                MessageBox.Show($"Lỗi dữ liệu: {exceptionMessage}", "Error", MessageBoxButtons.OK,
                    MessageBoxIcon.Warning);
                e.ThrowException = false; // Ngăn chặn lỗi làm gián đoạn chương trình
            }
        }

        private void UpdateRowIsChecked(bool isFavorite = false) {
            DataGridViewRow? currentRow = dgvGameInfo.CurrentRow;
            if (currentRow == null) {
                return;
            }

            string package = currentRow.Cells["Package"].Value.ToString();

            //Update DB
            string query;
            if (isFavorite) {
                query = "UPDATE Game_Android SET isFavorite = 1 WHERE package = @package";
            } else {
                query = "UPDATE Game_Android SET isChecked = 1 WHERE package = @package";
            }

            using (SqliteCommand cmd = new SqliteCommand(query, Home.GetConnection())) {
                cmd.Parameters.AddWithValue("@package", package);
                cmd.ExecuteNonQuery();
            }

            //Update DataGridView
            List<GameInfo> gameInfos = (List<GameInfo>) dgvGameInfo.DataSource;
            GameInfo dataBoundItem = (GameInfo) currentRow.DataBoundItem;
            gameInfos.Remove(dataBoundItem);
            _gameInfos.Remove(dataBoundItem);

            if (gameInfos.Count > 0) {
                dgvGameInfo.Refresh();
                GetInfoFromCurrentRow();
                txtTotal.Text = gameInfos.Count.ToString();

            } else {
                btnClear_Click(null, null);
            }
        }
    }
}