using Microsoft.Data.Sqlite;

namespace DataAICrawler {
    public partial class Home : Form {
        private static SqliteConnection connection;

        public  CrawlerData crawlerData;
        private ProcessData _processData;

        public static Home instance;

        public Home() {
            instance = this;
            InitializeComponent();
        }

        private async void btnCrawler_Click(object sender, EventArgs e) {
            await InitWebView(true);
        }

        public static SqliteConnection GetConnection() {
            if (connection != null) {
                return connection;
            }

            SQLitePCL.raw.SetProvider(new SQLitePCL.SQLite3Provider_e_sqlite3());
            string db = "Data Source=E:\\Projects\\VisualStudioProjects\\DataAI_Crawler\\Data\\db_game.db";
            connection = new SqliteConnection(db);
            connection.Open();
            return connection;
        }

        private void btnProcessData_Click(object sender, EventArgs e) {
            if (_processData == null || _processData.IsDisposed) {
                _processData = new ProcessData();
                _processData.Show();
            } else if (_processData.Visible) {
                _processData.Activate();
            } else {
                _processData.Show();
            }
        }

        public async Task InitWebView(bool isActivate = true) {
            if (crawlerData == null || crawlerData.IsDisposed) {
                crawlerData = new CrawlerData();
                crawlerData.Show();
            } else if (crawlerData.Visible) {
                if (isActivate) {
                    crawlerData.Activate();
                }
            } else {
                crawlerData.Show();
            }

            while (crawlerData.IsInited() == false) {
                await Task.Delay(1000);
            }
        }
    }
}