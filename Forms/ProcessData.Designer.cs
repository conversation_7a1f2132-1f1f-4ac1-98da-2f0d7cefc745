
namespace DataAICrawler {
    partial class ProcessData {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing) {
            if (disposing && (components != null)) {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent() {
            btnLoad = new Button();
            dgvGameInfo = new DataGridView();
            label1 = new Label();
            label2 = new Label();
            txtName = new TextBox();
            txtPackageName = new TextBox();
            btnApkUtils = new Button();
            btnSetChecked = new Button();
            txtTotal = new Label();
            btnSearch = new Button();
            txtSearch = new TextBox();
            btnClear = new Button();
            txtPublisher = new TextBox();
            label3 = new Label();
            btnUpdateRow = new Button();
            btnStartUpdate = new Button();
            gbRowInfo = new GroupBox();
            gbSearch = new GroupBox();
            label6 = new Label();
            txtAvailableDate = new TextBox();
            label5 = new Label();
            txtLastUpdateDate = new TextBox();
            label4 = new Label();
            groupBox1 = new GroupBox();
            cbIsFavorite = new CheckBox();
            cbIsChecked = new CheckBox();
            btnSetFavorite = new Button();
            btnOpenWebView = new Button();
            ((System.ComponentModel.ISupportInitialize)dgvGameInfo).BeginInit();
            gbRowInfo.SuspendLayout();
            gbSearch.SuspendLayout();
            groupBox1.SuspendLayout();
            SuspendLayout();
            // 
            // btnLoad
            // 
            btnLoad.Location = new Point(296, 18);
            btnLoad.Name = "btnLoad";
            btnLoad.Size = new Size(75, 23);
            btnLoad.TabIndex = 0;
            btnLoad.Text = "Load";
            btnLoad.UseVisualStyleBackColor = true;
            btnLoad.Click += btnLoad_Click;
            // 
            // dgvGameInfo
            // 
            dgvGameInfo.AllowUserToAddRows = false;
            dgvGameInfo.AllowUserToDeleteRows = false;
            dgvGameInfo.AllowUserToOrderColumns = true;
            dgvGameInfo.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dgvGameInfo.Location = new Point(12, 300);
            dgvGameInfo.Name = "dgvGameInfo";
            dgvGameInfo.ReadOnly = true;
            dgvGameInfo.Size = new Size(761, 448);
            dgvGameInfo.TabIndex = 1;
            dgvGameInfo.CellFormatting += dgvGameInfo_CellFormatting;
            dgvGameInfo.ColumnHeaderMouseClick += dgvGameInfo_ColumnHeaderMouseClick;
            dgvGameInfo.DataError += dgvGameInfo_DataError;
            dgvGameInfo.SelectionChanged += dgvGameInfo_SelectionChanged;
            // 
            // label1
            // 
            label1.AutoSize = true;
            label1.Location = new Point(9, 30);
            label1.Name = "label1";
            label1.Size = new Size(39, 15);
            label1.TabIndex = 2;
            label1.Text = "Name";
            // 
            // label2
            // 
            label2.AutoSize = true;
            label2.Location = new Point(9, 59);
            label2.Name = "label2";
            label2.Size = new Size(86, 15);
            label2.TabIndex = 3;
            label2.Text = "Package Name";
            // 
            // txtName
            // 
            txtName.Location = new Point(101, 22);
            txtName.Name = "txtName";
            txtName.Size = new Size(267, 23);
            txtName.TabIndex = 6;
            // 
            // txtPackageName
            // 
            txtPackageName.Location = new Point(101, 51);
            txtPackageName.Name = "txtPackageName";
            txtPackageName.Size = new Size(267, 23);
            txtPackageName.TabIndex = 7;
            // 
            // btnApkUtils
            // 
            btnApkUtils.Location = new Point(296, 47);
            btnApkUtils.Name = "btnApkUtils";
            btnApkUtils.Size = new Size(75, 23);
            btnApkUtils.TabIndex = 8;
            btnApkUtils.Text = "ApkUtils";
            btnApkUtils.UseVisualStyleBackColor = true;
            btnApkUtils.Click += btnApkUtils_Click;
            // 
            // btnSetChecked
            // 
            btnSetChecked.Location = new Point(394, 174);
            btnSetChecked.Name = "btnSetChecked";
            btnSetChecked.Size = new Size(96, 23);
            btnSetChecked.TabIndex = 9;
            btnSetChecked.Text = "Set Checked";
            btnSetChecked.UseVisualStyleBackColor = true;
            btnSetChecked.Click += btnSetChecked_Click;
            // 
            // txtTotal
            // 
            txtTotal.AutoSize = true;
            txtTotal.Location = new Point(748, 279);
            txtTotal.Name = "txtTotal";
            txtTotal.Size = new Size(25, 15);
            txtTotal.TabIndex = 10;
            txtTotal.Text = "000";
            txtTotal.TextAlign = ContentAlignment.TopRight;
            // 
            // btnSearch
            // 
            btnSearch.Location = new Point(6, 109);
            btnSearch.Name = "btnSearch";
            btnSearch.Size = new Size(75, 23);
            btnSearch.TabIndex = 11;
            btnSearch.Text = "Search";
            btnSearch.UseVisualStyleBackColor = true;
            btnSearch.Click += btnSearch_Click;
            // 
            // txtSearch
            // 
            txtSearch.Location = new Point(108, 80);
            txtSearch.Name = "txtSearch";
            txtSearch.Size = new Size(201, 23);
            txtSearch.TabIndex = 12;
            // 
            // btnClear
            // 
            btnClear.Location = new Point(87, 109);
            btnClear.Name = "btnClear";
            btnClear.Size = new Size(75, 23);
            btnClear.TabIndex = 13;
            btnClear.Text = "Clear";
            btnClear.UseVisualStyleBackColor = true;
            btnClear.Click += btnClear_Click;
            // 
            // txtPublisher
            // 
            txtPublisher.Location = new Point(101, 80);
            txtPublisher.Name = "txtPublisher";
            txtPublisher.Size = new Size(267, 23);
            txtPublisher.TabIndex = 15;
            // 
            // label3
            // 
            label3.AutoSize = true;
            label3.Location = new Point(9, 88);
            label3.Name = "label3";
            label3.Size = new Size(56, 15);
            label3.TabIndex = 14;
            label3.Text = "Publisher";
            // 
            // btnUpdateRow
            // 
            btnUpdateRow.Location = new Point(395, 145);
            btnUpdateRow.Name = "btnUpdateRow";
            btnUpdateRow.Size = new Size(95, 23);
            btnUpdateRow.TabIndex = 16;
            btnUpdateRow.Text = "Update Row";
            btnUpdateRow.TextImageRelation = TextImageRelation.ImageAboveText;
            btnUpdateRow.UseVisualStyleBackColor = true;
            btnUpdateRow.Click += btnUpdateRow_Click;
            // 
            // btnStartUpdate
            // 
            btnStartUpdate.Location = new Point(496, 145);
            btnStartUpdate.Name = "btnStartUpdate";
            btnStartUpdate.Size = new Size(92, 23);
            btnStartUpdate.TabIndex = 17;
            btnStartUpdate.Text = "Start Update";
            btnStartUpdate.TextImageRelation = TextImageRelation.ImageAboveText;
            btnStartUpdate.UseVisualStyleBackColor = true;
            btnStartUpdate.Click += btnStartUpdate_Click;
            // 
            // gbRowInfo
            // 
            gbRowInfo.Controls.Add(txtName);
            gbRowInfo.Controls.Add(label1);
            gbRowInfo.Controls.Add(label2);
            gbRowInfo.Controls.Add(txtPublisher);
            gbRowInfo.Controls.Add(txtPackageName);
            gbRowInfo.Controls.Add(label3);
            gbRowInfo.Location = new Point(395, 12);
            gbRowInfo.Name = "gbRowInfo";
            gbRowInfo.Size = new Size(378, 119);
            gbRowInfo.TabIndex = 18;
            gbRowInfo.TabStop = false;
            gbRowInfo.Text = "Row  Info";
            // 
            // gbSearch
            // 
            gbSearch.Controls.Add(label6);
            gbSearch.Controls.Add(txtAvailableDate);
            gbSearch.Controls.Add(label5);
            gbSearch.Controls.Add(txtLastUpdateDate);
            gbSearch.Controls.Add(label4);
            gbSearch.Controls.Add(txtSearch);
            gbSearch.Controls.Add(btnSearch);
            gbSearch.Controls.Add(btnClear);
            gbSearch.Location = new Point(12, 137);
            gbSearch.Name = "gbSearch";
            gbSearch.Size = new Size(377, 157);
            gbSearch.TabIndex = 19;
            gbSearch.TabStop = false;
            gbSearch.Text = "Search";
            // 
            // label6
            // 
            label6.AutoSize = true;
            label6.Location = new Point(6, 25);
            label6.Name = "label6";
            label6.Size = new Size(82, 15);
            label6.TabIndex = 20;
            label6.Text = "Available Date";
            // 
            // txtAvailableDate
            // 
            txtAvailableDate.Location = new Point(108, 22);
            txtAvailableDate.Name = "txtAvailableDate";
            txtAvailableDate.Size = new Size(201, 23);
            txtAvailableDate.TabIndex = 19;
            // 
            // label5
            // 
            label5.AutoSize = true;
            label5.Location = new Point(6, 54);
            label5.Name = "label5";
            label5.Size = new Size(96, 15);
            label5.TabIndex = 18;
            label5.Text = "Last Update Date";
            // 
            // txtLastUpdateDate
            // 
            txtLastUpdateDate.Location = new Point(108, 51);
            txtLastUpdateDate.Name = "txtLastUpdateDate";
            txtLastUpdateDate.Size = new Size(201, 23);
            txtLastUpdateDate.TabIndex = 17;
            // 
            // label4
            // 
            label4.AutoSize = true;
            label4.Location = new Point(6, 83);
            label4.Name = "label4";
            label4.Size = new Size(28, 15);
            label4.TabIndex = 16;
            label4.Text = "Text";
            // 
            // groupBox1
            // 
            groupBox1.Controls.Add(cbIsFavorite);
            groupBox1.Controls.Add(cbIsChecked);
            groupBox1.Controls.Add(btnLoad);
            groupBox1.Controls.Add(btnApkUtils);
            groupBox1.Location = new Point(12, 12);
            groupBox1.Name = "groupBox1";
            groupBox1.Size = new Size(377, 119);
            groupBox1.TabIndex = 20;
            groupBox1.TabStop = false;
            groupBox1.Text = "Filter";
            // 
            // cbIsFavorite
            // 
            cbIsFavorite.AutoSize = true;
            cbIsFavorite.Location = new Point(6, 47);
            cbIsFavorite.Name = "cbIsFavorite";
            cbIsFavorite.Size = new Size(79, 19);
            cbIsFavorite.TabIndex = 2;
            cbIsFavorite.Text = "Is Favorite";
            cbIsFavorite.UseVisualStyleBackColor = true;
            // 
            // cbIsChecked
            // 
            cbIsChecked.AutoSize = true;
            cbIsChecked.Location = new Point(6, 22);
            cbIsChecked.Name = "cbIsChecked";
            cbIsChecked.Size = new Size(83, 19);
            cbIsChecked.TabIndex = 1;
            cbIsChecked.Text = "Is Checked";
            cbIsChecked.UseVisualStyleBackColor = true;
            // 
            // btnSetFavorite
            // 
            btnSetFavorite.Location = new Point(496, 174);
            btnSetFavorite.Name = "btnSetFavorite";
            btnSetFavorite.Size = new Size(92, 23);
            btnSetFavorite.TabIndex = 21;
            btnSetFavorite.Text = "Set Favorite";
            btnSetFavorite.UseVisualStyleBackColor = true;
            btnSetFavorite.Click += btnSetFavorite_Click;
            // 
            // btnOpenWebView
            // 
            btnOpenWebView.Location = new Point(594, 145);
            btnOpenWebView.Name = "btnOpenWebView";
            btnOpenWebView.Size = new Size(95, 23);
            btnOpenWebView.TabIndex = 22;
            btnOpenWebView.Text = "WebView";
            btnOpenWebView.TextImageRelation = TextImageRelation.ImageAboveText;
            btnOpenWebView.UseVisualStyleBackColor = true;
            btnOpenWebView.Click += btnOpenWebView_ClickAsync;
            // 
            // ProcessData
            // 
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(785, 760);
            Controls.Add(btnOpenWebView);
            Controls.Add(btnSetFavorite);
            Controls.Add(groupBox1);
            Controls.Add(gbSearch);
            Controls.Add(gbRowInfo);
            Controls.Add(btnStartUpdate);
            Controls.Add(btnUpdateRow);
            Controls.Add(txtTotal);
            Controls.Add(btnSetChecked);
            Controls.Add(dgvGameInfo);
            Name = "ProcessData";
            Text = "ProcessData";
            ((System.ComponentModel.ISupportInitialize)dgvGameInfo).EndInit();
            gbRowInfo.ResumeLayout(false);
            gbRowInfo.PerformLayout();
            gbSearch.ResumeLayout(false);
            gbSearch.PerformLayout();
            groupBox1.ResumeLayout(false);
            groupBox1.PerformLayout();
            ResumeLayout(false);
            PerformLayout();
        }

        #endregion

        private Button btnLoad;
        private DataGridView dgvGameInfo;
        private Label label1;
        private Label label2;
        private TextBox txtName;
        private TextBox txtPackageName;
        private Button btnApkUtils;
        private Button btnSetChecked;
        private Label txtTotal;
        private Button btnSearch;
        private TextBox txtSearch;
        private Button btnClear;
        private TextBox txtPublisher;
        private Label label3;
        private Button btnUpdateRow;
        private Button btnStartUpdate;
        private GroupBox gbRowInfo;
        private GroupBox gbSearch;
        private GroupBox groupBox1;
        private CheckBox cbIsChecked;
        private CheckBox cbIsFavorite;
        private Button btnSetFavorite;
        private Button btnOpenWebView;
        private Label label4;
        private Label label6;
        private TextBox txtAvailableDate;
        private Label label5;
        private TextBox txtLastUpdateDate;
    }
}