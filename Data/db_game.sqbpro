<?xml version="1.0" encoding="UTF-8"?><sqlb_project><db path="E:/Projects/VisualStudioProjects/DataAI_Crawler/Data/db_game.db" readonly="0" foreign_keys="0" case_sensitive_like="0" temp_store="0" wal_autocheckpoint="0" synchronous="1"/><attached/><window><main_tabs open="structure browser pragmas query" current="1"/></window><tab_structure><column_width id="0" width="300"/><column_width id="1" width="0"/><column_width id="2" width="100"/><column_width id="3" width="1907"/><column_width id="4" width="0"/><expanded_item id="0" parent="1"/><expanded_item id="1" parent="1"/><expanded_item id="2" parent="1"/><expanded_item id="3" parent="1"/></tab_structure><tab_browse><current_table name="4,12:mainGame_Android"/><default_encoding codec=""/><browse_table_settings><table schema="main" name="Game_Android" show_row_id="0" encoding="" plot_x_axis="" unlock_view_pk="_rowid_"><sort/><column_widths/><filter_values><column index="3" value="ABI"/></filter_values><conditional_formats/><row_id_formats/><display_formats/><hidden_columns/><plot_y_axes/><global_filter/></table></browse_table_settings></tab_browse><tab_sql><sql name="SQL 1">SELECT package, name, publishName, urlIcon FROM Game_Android where isChecked = 0
SELECT package, name, publishName, urlIcon, downloads, dailyDownloads  FROM Game_Android where isChecked = 0

UPDATE Game_Android
SET isChecked = 0
WHERE package = &quot;com.dreamgames.royalmatch&quot;


UPDATE Game_Android
SET publishName = 'ABI Games Studio'
WHERE publishName IS '';
</sql><current_tab id="0"/></tab_sql></sqlb_project>
