using System;
using System.Windows.Forms;

namespace ApkUtils {
    partial class ApkUtilsForm {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing) {
            if (disposing && (components != null)) {
                components.Dispose();
            }

            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent() {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(ApkUtilsForm));
            txtApk = new TextBox();
            label1 = new Label();
            btnOpen = new Button();
            label2 = new Label();
            txtPackageName = new TextBox();
            rtxtResults = new RichTextBox();
            btnInstall = new Button();
            btnUninstall = new Button();
            btnOpenGame = new Button();
            btnClearApp = new Button();
            btnOpenStore = new Button();
            btnOpenPlayStore = new Button();
            btnOpenAppanie = new Button();
            btnOpenSensortower = new Button();
            btnOpenAppbrain = new Button();
            flowLayoutPanel1 = new FlowLayoutPanel();
            btnCalcFreeStorage = new Button();
            btnClearLog = new Button();
            flowLayoutPanel2 = new FlowLayoutPanel();
            label3 = new Label();
            btnRefresh = new Button();
            cmbDevices = new ComboBox();
            label4 = new Label();
            btnMoveApk = new Button();
            txtTargetDir = new TextBox();
            flowLayoutPanel3 = new FlowLayoutPanel();
            btnDeleteApk = new Button();
            btnDownApk = new Button();
            btnGetCurrentRun = new Button();
            label5 = new Label();
            txtLabel = new TextBox();
            label6 = new Label();
            txtActivityName = new TextBox();
            groupBox1 = new GroupBox();
            groupBox2 = new GroupBox();
            txtIP = new TextBox();
            btnConnectIP = new Button();
            flowLayoutPanel1.SuspendLayout();
            flowLayoutPanel2.SuspendLayout();
            flowLayoutPanel3.SuspendLayout();
            groupBox1.SuspendLayout();
            groupBox2.SuspendLayout();
            SuspendLayout();
            // 
            // txtApk
            // 
            txtApk.AllowDrop = true;
            txtApk.Location = new Point(125, 47);
            txtApk.Margin = new Padding(4, 3, 4, 3);
            txtApk.Name = "txtApk";
            txtApk.Size = new Size(447, 23);
            txtApk.TabIndex = 0;
            txtApk.TextAlign = HorizontalAlignment.Right;
            txtApk.TextChanged += TxtApk_TextChanged;
            txtApk.DragDrop += TxtApk_DragDrop;
            txtApk.DragEnter += TxtApk_DragEnter;
            // 
            // label1
            // 
            label1.Location = new Point(15, 47);
            label1.Margin = new Padding(4, 0, 4, 0);
            label1.Name = "label1";
            label1.Size = new Size(103, 23);
            label1.TabIndex = 1;
            label1.Text = "Apk File";
            label1.TextAlign = ContentAlignment.MiddleLeft;
            // 
            // btnOpen
            // 
            btnOpen.Location = new Point(580, 45);
            btnOpen.Margin = new Padding(4, 3, 4, 3);
            btnOpen.Name = "btnOpen";
            btnOpen.Size = new Size(92, 27);
            btnOpen.TabIndex = 2;
            btnOpen.Text = "Open";
            btnOpen.UseVisualStyleBackColor = true;
            btnOpen.Click += BtnOpen_Click;
            // 
            // label2
            // 
            label2.Location = new Point(15, 77);
            label2.Margin = new Padding(4, 0, 4, 0);
            label2.Name = "label2";
            label2.Size = new Size(103, 23);
            label2.TabIndex = 4;
            label2.Text = "Package Name";
            label2.TextAlign = ContentAlignment.MiddleLeft;
            // 
            // txtPackageName
            // 
            txtPackageName.Location = new Point(125, 77);
            txtPackageName.Margin = new Padding(4, 3, 4, 3);
            txtPackageName.Name = "txtPackageName";
            txtPackageName.Size = new Size(447, 23);
            txtPackageName.TabIndex = 3;
            txtPackageName.TextChanged += OnPackageName_TextChanged;
            // 
            // rtxtResults
            // 
            rtxtResults.AllowDrop = true;
            rtxtResults.Location = new Point(19, 313);
            rtxtResults.Margin = new Padding(4, 3, 4, 3);
            rtxtResults.Name = "rtxtResults";
            rtxtResults.Size = new Size(654, 243);
            rtxtResults.TabIndex = 5;
            rtxtResults.Text = "";
            rtxtResults.DragDrop += rtxtResults_DragDrop;
            rtxtResults.DragEnter += rtxtResults_DragEnter;
            rtxtResults.TextChanged += rtxtResults_TextChanged;
            // 
            // btnInstall
            // 
            btnInstall.Location = new Point(4, 3);
            btnInstall.Margin = new Padding(4, 3, 4, 3);
            btnInstall.Name = "btnInstall";
            btnInstall.Size = new Size(88, 27);
            btnInstall.TabIndex = 6;
            btnInstall.Text = "Install";
            btnInstall.UseVisualStyleBackColor = true;
            btnInstall.Click += BtnInstall_Click;
            // 
            // btnUninstall
            // 
            btnUninstall.Location = new Point(100, 3);
            btnUninstall.Margin = new Padding(4, 3, 4, 3);
            btnUninstall.Name = "btnUninstall";
            btnUninstall.Size = new Size(88, 27);
            btnUninstall.TabIndex = 7;
            btnUninstall.Text = "Uninstall";
            btnUninstall.UseVisualStyleBackColor = true;
            btnUninstall.Click += BtnUninstall_Click;
            // 
            // btnOpenGame
            // 
            btnOpenGame.Location = new Point(196, 3);
            btnOpenGame.Margin = new Padding(4, 3, 4, 3);
            btnOpenGame.Name = "btnOpenGame";
            btnOpenGame.Size = new Size(88, 27);
            btnOpenGame.TabIndex = 8;
            btnOpenGame.Text = "Open Game";
            btnOpenGame.UseVisualStyleBackColor = true;
            btnOpenGame.Click += BtnOpenGame_Click;
            // 
            // btnClearApp
            // 
            btnClearApp.Location = new Point(292, 3);
            btnClearApp.Margin = new Padding(4, 3, 4, 3);
            btnClearApp.Name = "btnClearApp";
            btnClearApp.Size = new Size(107, 27);
            btnClearApp.TabIndex = 9;
            btnClearApp.Text = "Clear Appdata";
            btnClearApp.UseVisualStyleBackColor = true;
            btnClearApp.Click += BtnClearApp_Click;
            // 
            // btnOpenStore
            // 
            btnOpenStore.Location = new Point(407, 3);
            btnOpenStore.Margin = new Padding(4, 3, 4, 3);
            btnOpenStore.Name = "btnOpenStore";
            btnOpenStore.Size = new Size(107, 27);
            btnOpenStore.TabIndex = 10;
            btnOpenStore.Text = "Open on Store";
            btnOpenStore.UseVisualStyleBackColor = true;
            btnOpenStore.Click += BtnOpenStore_Click;
            // 
            // btnOpenPlayStore
            // 
            btnOpenPlayStore.Location = new Point(4, 3);
            btnOpenPlayStore.Margin = new Padding(4, 3, 4, 3);
            btnOpenPlayStore.Name = "btnOpenPlayStore";
            btnOpenPlayStore.Size = new Size(134, 27);
            btnOpenPlayStore.TabIndex = 11;
            btnOpenPlayStore.Text = "Open on PlayStore";
            btnOpenPlayStore.UseVisualStyleBackColor = true;
            btnOpenPlayStore.Click += BtnOpenPlayStore_Click;
            // 
            // btnOpenAppanie
            // 
            btnOpenAppanie.Location = new Point(146, 3);
            btnOpenAppanie.Margin = new Padding(4, 3, 4, 3);
            btnOpenAppanie.Name = "btnOpenAppanie";
            btnOpenAppanie.Size = new Size(134, 27);
            btnOpenAppanie.TabIndex = 12;
            btnOpenAppanie.Text = "Open on Appanie";
            btnOpenAppanie.UseVisualStyleBackColor = true;
            btnOpenAppanie.Click += BtnOpenAppanie_Click;
            // 
            // btnOpenSensortower
            // 
            btnOpenSensortower.Location = new Point(288, 3);
            btnOpenSensortower.Margin = new Padding(4, 3, 4, 3);
            btnOpenSensortower.Name = "btnOpenSensortower";
            btnOpenSensortower.Size = new Size(148, 27);
            btnOpenSensortower.TabIndex = 13;
            btnOpenSensortower.Text = "Open on Sensortower";
            btnOpenSensortower.UseVisualStyleBackColor = true;
            btnOpenSensortower.Click += BtnOpenSensortower_Click;
            // 
            // btnOpenAppbrain
            // 
            btnOpenAppbrain.Location = new Point(444, 3);
            btnOpenAppbrain.Margin = new Padding(4, 3, 4, 3);
            btnOpenAppbrain.Name = "btnOpenAppbrain";
            btnOpenAppbrain.Size = new Size(134, 27);
            btnOpenAppbrain.TabIndex = 14;
            btnOpenAppbrain.Text = "Open on Appbrain";
            btnOpenAppbrain.UseVisualStyleBackColor = true;
            btnOpenAppbrain.Click += BtnOpenAppbrain_Click;
            // 
            // flowLayoutPanel1
            // 
            flowLayoutPanel1.Controls.Add(btnInstall);
            flowLayoutPanel1.Controls.Add(btnUninstall);
            flowLayoutPanel1.Controls.Add(btnOpenGame);
            flowLayoutPanel1.Controls.Add(btnClearApp);
            flowLayoutPanel1.Controls.Add(btnOpenStore);
            flowLayoutPanel1.Controls.Add(btnCalcFreeStorage);
            flowLayoutPanel1.Dock = DockStyle.Fill;
            flowLayoutPanel1.Location = new Point(4, 19);
            flowLayoutPanel1.Margin = new Padding(4, 3, 4, 3);
            flowLayoutPanel1.Name = "flowLayoutPanel1";
            flowLayoutPanel1.Size = new Size(650, 36);
            flowLayoutPanel1.TabIndex = 15;
            // 
            // btnCalcFreeStorage
            // 
            btnCalcFreeStorage.Location = new Point(4, 36);
            btnCalcFreeStorage.Margin = new Padding(4, 3, 4, 3);
            btnCalcFreeStorage.Name = "btnCalcFreeStorage";
            btnCalcFreeStorage.Size = new Size(132, 27);
            btnCalcFreeStorage.TabIndex = 11;
            btnCalcFreeStorage.Text = "Calc Free Storage";
            btnCalcFreeStorage.UseVisualStyleBackColor = true;
            btnCalcFreeStorage.Click += BtnCalcFreeStorage_Click;
            // 
            // btnClearLog
            // 
            btnClearLog.Location = new Point(18, 563);
            btnClearLog.Margin = new Padding(4, 3, 4, 3);
            btnClearLog.Name = "btnClearLog";
            btnClearLog.Size = new Size(107, 27);
            btnClearLog.TabIndex = 11;
            btnClearLog.Text = "Clear Log";
            btnClearLog.UseVisualStyleBackColor = true;
            btnClearLog.Click += BtnClearLog_Click;
            // 
            // flowLayoutPanel2
            // 
            flowLayoutPanel2.Controls.Add(btnOpenPlayStore);
            flowLayoutPanel2.Controls.Add(btnOpenAppanie);
            flowLayoutPanel2.Controls.Add(btnOpenSensortower);
            flowLayoutPanel2.Controls.Add(btnOpenAppbrain);
            flowLayoutPanel2.Dock = DockStyle.Fill;
            flowLayoutPanel2.Location = new Point(4, 19);
            flowLayoutPanel2.Margin = new Padding(4, 3, 4, 3);
            flowLayoutPanel2.Name = "flowLayoutPanel2";
            flowLayoutPanel2.Size = new Size(650, 37);
            flowLayoutPanel2.TabIndex = 16;
            // 
            // label3
            // 
            label3.Location = new Point(14, 14);
            label3.Margin = new Padding(4, 0, 4, 0);
            label3.Name = "label3";
            label3.Size = new Size(103, 23);
            label3.TabIndex = 18;
            label3.Text = "Device";
            label3.TextAlign = ContentAlignment.MiddleLeft;
            // 
            // btnRefresh
            // 
            btnRefresh.Location = new Point(336, 12);
            btnRefresh.Margin = new Padding(4, 3, 4, 3);
            btnRefresh.Name = "btnRefresh";
            btnRefresh.Size = new Size(92, 27);
            btnRefresh.TabIndex = 19;
            btnRefresh.Text = "Refresh";
            btnRefresh.UseVisualStyleBackColor = true;
            btnRefresh.Click += BtnRefresh_Click;
            // 
            // cmbDevices
            // 
            cmbDevices.FormattingEnabled = true;
            cmbDevices.Location = new Point(125, 13);
            cmbDevices.Margin = new Padding(4, 3, 4, 3);
            cmbDevices.Name = "cmbDevices";
            cmbDevices.Size = new Size(204, 23);
            cmbDevices.TabIndex = 20;
            // 
            // label4
            // 
            label4.Location = new Point(505, 563);
            label4.Margin = new Padding(4, 0, 4, 0);
            label4.Name = "label4";
            label4.Size = new Size(168, 23);
            label4.TabIndex = 21;
            label4.Text = "<EMAIL>";
            label4.TextAlign = ContentAlignment.MiddleRight;
            // 
            // btnMoveApk
            // 
            btnMoveApk.Location = new Point(4, 3);
            btnMoveApk.Margin = new Padding(4, 3, 4, 3);
            btnMoveApk.Name = "btnMoveApk";
            btnMoveApk.Size = new Size(93, 27);
            btnMoveApk.TabIndex = 22;
            btnMoveApk.Text = "Move apk to";
            btnMoveApk.UseVisualStyleBackColor = true;
            btnMoveApk.Click += BtnMoveApk_Click;
            // 
            // txtTargetDir
            // 
            txtTargetDir.Location = new Point(105, 3);
            txtTargetDir.Margin = new Padding(4, 3, 4, 3);
            txtTargetDir.Name = "txtTargetDir";
            txtTargetDir.Size = new Size(237, 23);
            txtTargetDir.TabIndex = 23;
            txtTargetDir.Text = "D:\\APKs";
            // 
            // flowLayoutPanel3
            // 
            flowLayoutPanel3.Controls.Add(btnMoveApk);
            flowLayoutPanel3.Controls.Add(txtTargetDir);
            flowLayoutPanel3.Controls.Add(btnDeleteApk);
            flowLayoutPanel3.Controls.Add(btnDownApk);
            flowLayoutPanel3.Location = new Point(20, 140);
            flowLayoutPanel3.Margin = new Padding(4, 3, 4, 3);
            flowLayoutPanel3.Name = "flowLayoutPanel3";
            flowLayoutPanel3.Size = new Size(563, 36);
            flowLayoutPanel3.TabIndex = 24;
            // 
            // btnDeleteApk
            // 
            btnDeleteApk.Location = new Point(350, 3);
            btnDeleteApk.Margin = new Padding(4, 3, 4, 3);
            btnDeleteApk.Name = "btnDeleteApk";
            btnDeleteApk.Size = new Size(93, 27);
            btnDeleteApk.TabIndex = 24;
            btnDeleteApk.Text = "Delete apk";
            btnDeleteApk.UseVisualStyleBackColor = true;
            btnDeleteApk.Click += BtnDeleteApk_Click;
            // 
            // btnDownApk
            // 
            btnDownApk.Location = new Point(451, 3);
            btnDownApk.Margin = new Padding(4, 3, 4, 3);
            btnDownApk.Name = "btnDownApk";
            btnDownApk.Size = new Size(104, 27);
            btnDownApk.TabIndex = 25;
            btnDownApk.Text = "Download apk";
            btnDownApk.UseVisualStyleBackColor = true;
            btnDownApk.Click += BtnDownApk_Click;
            // 
            // btnGetCurrentRun
            // 
            btnGetCurrentRun.Location = new Point(580, 75);
            btnGetCurrentRun.Margin = new Padding(4, 3, 4, 3);
            btnGetCurrentRun.Name = "btnGetCurrentRun";
            btnGetCurrentRun.Size = new Size(92, 27);
            btnGetCurrentRun.TabIndex = 25;
            btnGetCurrentRun.Text = "Current Run";
            btnGetCurrentRun.UseVisualStyleBackColor = true;
            btnGetCurrentRun.Click += BtnGetCurrentRun_Click;
            // 
            // label5
            // 
            label5.Location = new Point(15, 107);
            label5.Margin = new Padding(4, 0, 4, 0);
            label5.Name = "label5";
            label5.Size = new Size(103, 23);
            label5.TabIndex = 27;
            label5.Text = "Label";
            label5.TextAlign = ContentAlignment.MiddleLeft;
            // 
            // txtLabel
            // 
            txtLabel.Location = new Point(125, 107);
            txtLabel.Margin = new Padding(4, 3, 4, 3);
            txtLabel.Name = "txtLabel";
            txtLabel.Size = new Size(236, 23);
            txtLabel.TabIndex = 26;
            // 
            // label6
            // 
            label6.Location = new Point(369, 108);
            label6.Margin = new Padding(4, 0, 4, 0);
            label6.Name = "label6";
            label6.Size = new Size(59, 23);
            label6.TabIndex = 29;
            label6.Text = "Activity";
            label6.TextAlign = ContentAlignment.MiddleLeft;
            // 
            // txtActivityName
            // 
            txtActivityName.Location = new Point(435, 108);
            txtActivityName.Margin = new Padding(4, 3, 4, 3);
            txtActivityName.Name = "txtActivityName";
            txtActivityName.Size = new Size(236, 23);
            txtActivityName.TabIndex = 28;
            // 
            // groupBox1
            // 
            groupBox1.Controls.Add(flowLayoutPanel2);
            groupBox1.Location = new Point(18, 247);
            groupBox1.Margin = new Padding(4, 3, 4, 3);
            groupBox1.Name = "groupBox1";
            groupBox1.Padding = new Padding(4, 3, 4, 3);
            groupBox1.Size = new Size(658, 59);
            groupBox1.TabIndex = 30;
            groupBox1.TabStop = false;
            groupBox1.Text = "PC";
            // 
            // groupBox2
            // 
            groupBox2.Controls.Add(flowLayoutPanel1);
            groupBox2.Location = new Point(18, 182);
            groupBox2.Margin = new Padding(4, 3, 4, 3);
            groupBox2.Name = "groupBox2";
            groupBox2.Padding = new Padding(4, 3, 4, 3);
            groupBox2.Size = new Size(658, 58);
            groupBox2.TabIndex = 31;
            groupBox2.TabStop = false;
            groupBox2.Text = "Mobile";
            // 
            // txtIP
            // 
            txtIP.AllowDrop = true;
            txtIP.Location = new Point(435, 13);
            txtIP.Margin = new Padding(4, 3, 4, 3);
            txtIP.Name = "txtIP";
            txtIP.Size = new Size(137, 23);
            txtIP.TabIndex = 32;
            // 
            // btnConnectIP
            // 
            btnConnectIP.Location = new Point(580, 10);
            btnConnectIP.Margin = new Padding(4, 3, 4, 3);
            btnConnectIP.Name = "btnConnectIP";
            btnConnectIP.Size = new Size(92, 27);
            btnConnectIP.TabIndex = 33;
            btnConnectIP.Text = "Connect IP";
            btnConnectIP.UseVisualStyleBackColor = true;
            btnConnectIP.Click += BtnConnectIP_Click;
            // 
            // ApkUtilsForm
            // 
            AllowDrop = true;
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(686, 597);
            Controls.Add(btnConnectIP);
            Controls.Add(txtIP);
            Controls.Add(groupBox2);
            Controls.Add(groupBox1);
            Controls.Add(label6);
            Controls.Add(txtActivityName);
            Controls.Add(btnClearLog);
            Controls.Add(label5);
            Controls.Add(txtLabel);
            Controls.Add(btnGetCurrentRun);
            Controls.Add(flowLayoutPanel3);
            Controls.Add(label4);
            Controls.Add(cmbDevices);
            Controls.Add(btnRefresh);
            Controls.Add(label3);
            Controls.Add(rtxtResults);
            Controls.Add(label2);
            Controls.Add(txtPackageName);
            Controls.Add(btnOpen);
            Controls.Add(label1);
            Controls.Add(txtApk);
            Icon = (Icon)resources.GetObject("$this.Icon");
            Margin = new Padding(4, 3, 4, 3);
            Name = "ApkUtilsForm";
            RightToLeftLayout = true;
            StartPosition = FormStartPosition.CenterScreen;
            Text = "Apk Utils";
            Load += ApkUtilsForm_Load;
            DragDrop += ApkUtilsForm_DragDrop;
            DragEnter += ApkUtilsForm_DragEnter;
            DoubleClick += ApkUtilsForm_DoubleClick;
            KeyDown += ApkUtilsForm_KeyDown;
            flowLayoutPanel1.ResumeLayout(false);
            flowLayoutPanel2.ResumeLayout(false);
            flowLayoutPanel3.ResumeLayout(false);
            flowLayoutPanel3.PerformLayout();
            groupBox1.ResumeLayout(false);
            groupBox2.ResumeLayout(false);
            ResumeLayout(false);
            PerformLayout();
        }



        #endregion

        private System.Windows.Forms.TextBox txtApk;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Button btnOpen;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.TextBox txtPackageName;
        private System.Windows.Forms.RichTextBox rtxtResults;
        private System.Windows.Forms.Button btnInstall;
        private System.Windows.Forms.Button btnUninstall;
        private System.Windows.Forms.Button btnOpenGame;
        private System.Windows.Forms.Button btnClearApp;
        private System.Windows.Forms.Button btnOpenStore;
        private System.Windows.Forms.Button btnOpenPlayStore;
        private System.Windows.Forms.Button btnOpenAppanie;
        private System.Windows.Forms.Button btnOpenSensortower;
        private System.Windows.Forms.Button btnOpenAppbrain;
        private System.Windows.Forms.FlowLayoutPanel flowLayoutPanel1;
        private System.Windows.Forms.FlowLayoutPanel flowLayoutPanel2;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Button btnRefresh;
        private System.Windows.Forms.ComboBox cmbDevices;
        private System.Windows.Forms.Button btnClearLog;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Button btnMoveApk;
        private System.Windows.Forms.TextBox txtTargetDir;
        private System.Windows.Forms.FlowLayoutPanel flowLayoutPanel3;
        private System.Windows.Forms.Button btnDeleteApk;
        private System.Windows.Forms.Button btnGetCurrentRun;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.TextBox txtLabel;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.TextBox txtActivityName;
        private System.Windows.Forms.Button btnDownApk;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.GroupBox groupBox2;
        private TextBox txtIP;
        private Button btnConnectIP;
        private Button btnCalcFreeStorage;
    }
}