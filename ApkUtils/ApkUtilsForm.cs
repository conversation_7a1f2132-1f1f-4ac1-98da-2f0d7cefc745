using ApkUtils;
using Microsoft.VisualBasic.FileIO;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.IO.Compression;
using System.Windows.Forms;
using trungvt.Utils;

namespace ApkUtils {
    public partial class ApkUtilsForm : Form {
        string filename;
        string packageName;
        string device => cmbDevices.Text.Trim() == "" ? "" : "-s " + cmbDevices.Text.Trim();

        public ApkUtilsForm() {
            InitializeComponent();
            BtnRefresh_Click(null, null);
        }

        private void ApkUtilsForm_Load(object sender, EventArgs e) {
            if (OpenFile != null) {
                txtApk.Text = OpenFile;
            }
        }

        private void ProcessShowLogAsync(string msg) {
            this.UIThread(() => {
                this.rtxtResults.AppendText(msg + "\n");
                this.Refresh();
            });
        }

        private void RunCmdPackageName(string cmdAdb) {
            if (packageName == null) {
                ProcessShowLogAsync("PackageName is NULL!");
                return;
            }

            Command.Execute(cmdAdb, (s) => ProcessShowLogAsync(s));
        }

        //######################################################################

        private void BtnOpen_Click(object sender, EventArgs e) {
            OpenFileDialog openFileDialog = new OpenFileDialog();
            openFileDialog.Filter = "apk,xapk files (*.*apk)|*.*apk";
            DialogResult result = openFileDialog.ShowDialog();
            if (result == DialogResult.OK) {
                filename = openFileDialog.FileName;

                if (txtApk.Text == filename) {
                    TxtApk_TextChanged(null, null);
                } else {
                    txtApk.Text = filename;
                }

            } else {
                filename = null;
            }
        }

        enum Type {
            apk,
            xapk
        }

        Type type;
        List<string> apks;
        internal string OpenFile;

        private void TxtApk_TextChanged(object sender, EventArgs e) {
            string tmpFilename = txtApk.Text.Trim();

            if (File.Exists(tmpFilename)) {
                if (!tmpFilename.ToLower().Contains(".apk") && !tmpFilename.ToLower().Contains(".xapk")) {
                    ProcessShowLogAsync("Only process apk|xapk file!");
                    return;
                }

                filename = tmpFilename; //need

                if (filename.Contains(".xapk")) {
                    type = Type.xapk;
                    apks = ProcessXapk(filename);
                    if (apks == null || apks.Count == 0) {
                        ProcessShowLogAsync("Cannot process: " + filename);
                    }

                    packageName = ApkUtils.getPackageName(apks[0]);
                    txtPackageName.Text = packageName;
                    processActivity(apks[0]);

                } else {
                    type = Type.apk;
                    packageName = ApkUtils.getPackageName(filename);
                    txtPackageName.Text = packageName;
                    processActivity(filename);
                }

            } else {
                txtPackageName.Clear();
                txtActivityName.Clear();
            }
        }

        private List<string> ProcessXapk(string filePath) {
            FileInfo f = new FileInfo(filePath);
            string tempPath = Path.Combine(Path.GetTempPath(), f.Name);
            string zipPath = FileUtils.GetZipPath(tempPath);

            File.Copy(filePath, zipPath, true);
            if (!File.Exists(zipPath)) {
                return null;
            }

            string folderPath = FileUtils.GetFolderPath(zipPath);
            if (Directory.Exists(folderPath)) {
                Directory.Delete(folderPath, true);
            }

            ZipFile.ExtractToDirectory(zipPath, folderPath);
            File.Delete(zipPath);
            if (!Directory.Exists(folderPath)) {
                return null;
            }

            string[] folders = Directory.GetDirectories(folderPath);
            if (ContainsFolder(folders, "Android")) {
                ProcessShowLogAsync("Cannot process folder: " + folderPath);
                return null;
            }

            string[] files = Directory.GetFiles(folderPath, "*.apk");
            List<string> apks = new List<string>();
            foreach (string item in files) {
                if (item.Contains("config")) {
                    apks.Add(item);
                } else {
                    apks.Insert(0, item);
                }
            }

            return apks;
        }

        private bool ContainsFolder(string[] folders, string name) {
            foreach (string item in folders) {
                if (item.Contains(name)) {
                    return true;
                }
            }

            return false;
        }

        private void processActivity(string filename) {
            string cmdAdb = String.Format("aapt dump badging \"{0}\" | findstr -n \"launchable-activity\"", filename);
            List<string> results = Command.Execute(cmdAdb);
            if (results.Count == 0) {
                return;
            }

            string[] result = results[0].Split('\'');
            txtActivityName.Text = result[1];
            txtLabel.Text = result[3];
        }

        private void BtnInstall_Click(object sender, EventArgs e) {
            try {
                if (type == Type.apk) {
                    if (string.IsNullOrEmpty(filename)) {
                        ProcessShowLogAsync("FileName is NULL!");
                        return;
                    }

                    string cmdAdb = string.Format("adb {0} install -r \"{1}\"", device, filename);
                    //   string cmdAdb = "ping 24h.com.vn";
                    Command.Execute(cmdAdb, (s) => ProcessShowLogAsync(s));

                } else if (type == Type.xapk) {
                    if (apks == null || apks.Count == 0) {
                        ProcessShowLogAsync("XAPK is NULL!");
                        return;
                    }

                    string cmdAdb = string.Format("adb {0} install-multiple -r ", device);
                    foreach (var item in apks) {
                        cmdAdb += " \"" + item + "\"";
                    }

                    Command.Execute(cmdAdb, (s) => ProcessShowLogAsync(s));

                } else {
                    ProcessShowLogAsync("Cannot process this type:" + type);
                }

            } catch (Exception ex) {
                ProcessShowLogAsync("[Exception]: " + ex.Message);
            }
        }

        private void BtnUninstall_Click(object sender, EventArgs e) {
            try {
                bool isShouldCancel = packageName.Contains("com.mi.android.globallauncher") ||
                                      packageName.Contains("com.android.settings") ||
                                      packageName.Contains("com.miui.securitycenter");

                if (isShouldCancel) {
                    ProcessShowLogAsync("Dont uninstall package name => " + packageName);
                    return;
                }

                bool isShouldCheck = packageName.Contains(".miui.") || packageName.Contains(".mi.") ||
                                     packageName.Contains(".android.");
                if (isShouldCheck) {
                    DialogResult dialogResult = MessageBox.Show("Uninstall " + packageName + "?", "Xác nhận",
                        MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                    if (dialogResult == DialogResult.Yes) {
                        Uninstall(packageName);
                    }
                } else {
                    Uninstall(packageName);
                }

            } catch (Exception ex) {
                ProcessShowLogAsync("[Exception]: " + ex.Message);
            }
        }

        private void Uninstall(string packageName) {
            string cmdAdb = string.Format("adb {0} uninstall {1}", device, packageName);
            RunCmdPackageName(cmdAdb);

            txtPackageName.Text = "";
            txtActivityName.Text = "";
        }

        private void BtnOpenGame_Click(object sender, EventArgs e) {
            try {
                string cmdAdb = string.Format("adb {0} shell am start -n {1}/{2}", device, packageName,
                    txtActivityName.Text.Trim());
                RunCmdPackageName(cmdAdb);
            } catch (Exception ex) {
                ProcessShowLogAsync("[Exception]: " + ex.Message);
            }
        }

        private void BtnClearApp_Click(object sender, EventArgs e) {
            try {
                string cmdAdb = string.Format("adb {0} shell pm clear {1}", device, packageName);
                RunCmdPackageName(cmdAdb);
            } catch (Exception ex) {
                ProcessShowLogAsync("[Exception]: " + ex.Message);
            }
        }

        private void BtnOpenStore_Click(object sender, EventArgs e) {
            try {
                string cmdAdb =
                    string.Format("adb {0} shell am start -a android.intent.action.VIEW -d market://details?id={1}",
                        device, packageName);
                RunCmdPackageName(cmdAdb);
            } catch (Exception ex) {
                ProcessShowLogAsync("[Exception]: " + ex.Message);
            }
        }

        #region Open brower

        private void BtnOpenPlayStore_Click(object sender, EventArgs e) {
            if (packageName == null) {
                ProcessShowLogAsync("PackageName is NULL!");
                return;
            }

            OpenWebLink("https://play.google.com/store/apps/details?id=" + packageName);
        }



        private void BtnOpenAppanie_Click(object sender, EventArgs e) {
            if (packageName == null) {
                ProcessShowLogAsync("PackageName is NULL!");
                return;
            }

            string link = "https://www.appannie.com/apps/google-play/app/" + packageName + "/details/";
            OpenWebLink(link);
        }

        private void BtnOpenSensortower_Click(object sender, EventArgs e) {
            if (packageName == null) {
                ProcessShowLogAsync("PackageName is NULL!");
                return;
            }

            string link = "https://sensortower.com/android/US/app/app/app/" + packageName + "/overview";
            OpenWebLink(link);
        }

        private void BtnOpenAppbrain_Click(object sender, EventArgs e) {
            if (packageName == null) {
                ProcessShowLogAsync("PackageName is NULL!");
                return;
            }

            string link = "https://www.appbrain.com/app/" + packageName;
            OpenWebLink(link);
        }

        #endregion

        private void OnPackageName_TextChanged(object sender, EventArgs e) {
            packageName = txtPackageName.Text.Trim();
        }

        private void rtxtResults_TextChanged(object sender, EventArgs e) {
            // set the current caret position to the end
            rtxtResults.SelectionStart = rtxtResults.Text.Length;
            // scroll it automatically
            rtxtResults.ScrollToCaret();
        }

        private void BtnRefresh_Click(object sender, EventArgs e) {
            try {
                string cmd = "adb devices";
                List<string> results = Command.Execute(cmd);

                cmbDevices.Items.Clear();

                if (results.Count > 1) {
                    for (int i = 1; i < results.Count; i++) {
                        string[] data = results[i].Split('\t');

                        if (data[1] == "device") {
                            string device = data[0];
                            cmbDevices.Items.Add(device);
                        }
                    }
                }

                if (cmbDevices.Items.Count > 0) {
                    cmbDevices.SelectedIndex = 0;
                } else {
                    cmbDevices.ResetText();
                }

            } catch (Exception ex) {
                ProcessShowLogAsync("[Exception]: " + ex.Message);
            }
        }

        private void BtnClearLog_Click(object sender, EventArgs e) {
            rtxtResults.Clear();
        }

        private void BtnMoveApk_Click(object sender, EventArgs e) {
            try {
                if (string.IsNullOrEmpty(filename)) {
                    ProcessShowLogAsync("FileName is NULL!");
                    return;
                }

                string targetDir = txtTargetDir.Text.Trim();
                ProcessShowLogAsync("Move to: " + targetDir);
                if (type == Type.xapk) {
                    string packageName = "";
                    if (apks != null && apks.Count > 0) {
                        packageName = ApkUtils.getPackageName(apks[0]);
                    }

                    string result = ApkUtils.InsterPackageNameAndMoveXapk(filename, targetDir, packageName);

                    ProcessShowLogAsync(result);

                } else {
                    string result = ApkUtils.InsterPackageNameAndMove(filename, targetDir);

                    ProcessShowLogAsync(result);
                }

            } catch (Exception ex) {
                ProcessShowLogAsync("[Exception]: " + ex.Message);
            }
        }

        private void BtnDeleteApk_Click(object sender, EventArgs e) {
            try {
                if (string.IsNullOrEmpty(filename)) {
                    ProcessShowLogAsync("FileName is NULL!");
                    return;
                }

                if (!File.Exists(filename)) {
                    ProcessShowLogAsync("FileName not found!");
                    return;
                }

                //File.Delete(filename);
                FileSystem.DeleteFile(filename, UIOption.OnlyErrorDialogs, RecycleOption.SendToRecycleBin);
                ProcessShowLogAsync("Delete!");

            } catch (Exception ex) {
                ProcessShowLogAsync("[Exception]: " + ex.Message);
            }
        }

        private void BtnGetCurrentRun_Click(object sender, EventArgs e) {
            try {
                txtApk.Text = "";
                txtLabel.Text = "";

                //string cmd = string.Format("adb {0} shell dumpsys activity recents | find  \"realActivity\"", device);
                //realActivity=com.asus.launcher/com.android.launcher3.Launcher

                //string cmd = string.Format("adb {0} shell dumpsys activity | find \"mResumedActivity\"", device);
                string cmd = string.Format("adb {0} shell dumpsys activity | findstr \"topResumedActivity mResumedActivity\"", device);
                List<string> results = Command.Execute(cmd);
                //mResumedActivity: ActivityRecord{4aa35ea8 u0 com.cnj.connectcell/com.unity3d.player.UnityPlayerActivity t2}

                if (results.Count > 0) {
                    string[] result = getFullPackage(results[0]).Split('/');
                    txtPackageName.Text = result[0];
                    txtActivityName.Text = result[1];
                } else {
                    txtPackageName.Text = "";
                    txtActivityName.Text = "";
                    ProcessShowLogAsync("Cannot get current process!");
                }
            } catch (Exception ex) {
                ProcessShowLogAsync("[Exception]: " + ex.Message);
            }
        }

        private string getFullPackage(string data) {
            string[] results = data.Split(' ');
            foreach (string item in results) {
                if (item.Contains("/")) {
                    return item;
                }
            }

            return null;
        }

        private void BtnDownApk_Click(object sender, EventArgs e) {
            if (packageName == null) {
                ProcessShowLogAsync("PackageName is NULL!");
                return;
            }

            string linkDownload = string.Format("https://apkpure.com/game/{0}/download?from=details", packageName);
            Utils.OpenBrower(linkDownload);
        }

        private void TxtApk_DragDrop(object sender, DragEventArgs e) {
            string[] files = (string[])e.Data.GetData(DataFormats.FileDrop, false);
            txtApk.Text = files[0];
        }

        private void TxtApk_DragEnter(object sender, DragEventArgs e) {
            if (e.Data.GetDataPresent(DataFormats.FileDrop, false) == true) {
                e.Effect = DragDropEffects.All;
            }
        }

        private void ApkUtilsForm_DragDrop(object sender, DragEventArgs e) {
            string[] files = (string[])e.Data.GetData(DataFormats.FileDrop, false);
            txtApk.Text = files[0];
        }

        private void ApkUtilsForm_DragEnter(object sender, DragEventArgs e) {
            if (e.Data.GetDataPresent(DataFormats.FileDrop, false) == true) {
                e.Effect = DragDropEffects.All;
            }
        }

        private void rtxtResults_DragDrop(object sender, DragEventArgs e) {
            string[] files = (string[])e.Data.GetData(DataFormats.FileDrop, false);
            txtApk.Text = files[0];
        }

        private void rtxtResults_DragEnter(object sender, DragEventArgs e) {
            if (e.Data.GetDataPresent(DataFormats.FileDrop, false) == true) {
                e.Effect = DragDropEffects.All;
            }
        }

        private void BtnConnectIP_Click(object sender, EventArgs e) {
            try {
                string cmd2 = "arp -a";
                List<string> results2 = Command.Execute(cmd2);
                string ipgeted = getIp(results2);
                txtIP.Text = ipgeted;
                ProcessShowLogAsync("[IP]: " + ipgeted);

                string cmd = "adb connect " + ipgeted + ":5555";
                ProcessShowLogAsync("[CMD]: " + cmd);
                List<string> results = Command.Execute(cmd);
                ShowLog(results);

            } catch (Exception ex) {
                ProcessShowLogAsync("[Exception]: " + ex.Message);
            }
        }

        private string getIp(List<string> results2) {
            for (int i = 0; i < results2.Count; i++) {
                string line = results2[i].Trim();
                if (line.Contains("60-45-cb-40-06-f3")) {
                    string[] s = line.Split(' ');
                    string s1 = s[0];
                    return s1;
                }
            }

            return null;
        }

        private void ShowLog(List<string> results) {
            for (int i = 0; i < results.Count; i++) {
                ProcessShowLogAsync("[" + (i + 1) + "/" + results.Count + "]: " + results[i]);
            }
        }

        private void BtnCalcFreeStorage_Click(object sender, EventArgs e) {
            try {
                string cmd = string.Format("adb {0} shell df -h |findstr \"storage\"", device);
                List<string> results = Command.Execute(cmd);

                if (results.Count > 0) {
                    string data = results[0]; ///data/media            23G   22G  866M  97% /storage/emulated
                    data = Utils.RemoveDoubleSpace(data);
                    string freeStorage = data.Split(' ')[3]; // 866M
                    char type = freeStorage.ToCharArray()[freeStorage.Length - 1];
                    freeStorage = freeStorage.Replace(type, ' ').Trim();
                    float storage;
                    if (type == 'G') {
                        storage = float.Parse(freeStorage) * 1000 - 500;
                    } else {
                        storage = float.Parse(freeStorage) - 500;
                    }

                    ProcessShowLogAsync("Free storage: " + storage + "M");
                }
            } catch (Exception ex) {
                ProcessShowLogAsync("[Exception]: " + ex.Message);
            }
        }

        public void SetPackage(string text) {
            txtPackageName.Text = text;
        }

        private void ApkUtilsForm_KeyDown(object sender, KeyEventArgs e) {
            if (e.KeyCode == Keys.Escape) {
                this.Hide();
            }
        }

        private void ApkUtilsForm_DoubleClick(object sender, EventArgs e) {
            this.Hide();
        }

        private void OpenWebLink(string url) {
            Process.Start(new ProcessStartInfo(url) { UseShellExecute = true });
        }
    }
}