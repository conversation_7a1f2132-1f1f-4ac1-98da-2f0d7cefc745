using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Windows.Forms;

namespace ApkUtils {
    public static class Command {
        public static List<string> Execute(string cmd) {
            try {
                List<string> results = new List<string>();

                using (Process p = new Process()) {
                    p.StartInfo.CreateNoWindow = true;
                    p.StartInfo.RedirectStandardOutput = true;
                    p.StartInfo.UseShellExecute = false;
                    //                    string cmd =
                    //                        "aapt dump badging \"D:\\Projects\\YoGame Projects\\DancingBallV\\Build\\DancingBall-V_v0.0.1_1905231000_ARM_1905301340.apk\" | findstr -n \"package: name\" | findstr \"1:\"";
                    p.StartInfo.Arguments = "/c " + cmd;
                    p.StartInfo.FileName = @"C:\Windows\System32\cmd.exe";
                    p.Start();

                    //string line;
                    //while((line = p.StandardOutput.ReadLine()) != null) {
                    //    if(line != "") {
                    //        results.Add(line);
                    //    }
                    //}

                    while (!p.StandardOutput.EndOfStream) {
                        string line = p.StandardOutput.ReadLine();
                        if (line != "") {
                            results.Add(line);
                        }
                    }

                    p.WaitForExit();
                }

                return results;
            } catch (Exception e) {
                Console.WriteLine(e);
                return null;
            }
        }

        public static void Execute(string cmd, Action<string> result) {
            try {
                onResult = result;

                if (onResult != null) {
                    onResult.Invoke("[Start] " + cmd);
                }

                using (Process p = new Process()) {
                    p.StartInfo.CreateNoWindow = true;
                    p.StartInfo.RedirectStandardOutput = true;
                    p.StartInfo.UseShellExecute = false;
                    p.StartInfo.Arguments = "/c " + cmd;
                    p.StartInfo.FileName = @"C:\Windows\System32\cmd.exe";

                    // event handlers for output & error
                    p.OutputDataReceived += p_OutputDataReceived;
                    p.ErrorDataReceived += p_ErrorDataReceived;

                    p.EnableRaisingEvents = true;
                    p.Exited += new EventHandler(p_Exited);

                    p.Start();
                    p.BeginOutputReadLine();
                    p.WaitForExit();
                }

            } catch (Exception e) {
                if (onResult != null) {
                    onResult.Invoke("[Exception] " + e.Message);
                }

                Console.WriteLine(e);
            }
        }

        private static void p_Exited(object sender, EventArgs e) {
            if (onResult != null) {
                onResult.Invoke("[Finished]");
            }
        }

        static Action<string> onResult;

        static void p_ErrorDataReceived(object sender, DataReceivedEventArgs e) {
            Process p = sender as Process;
            if (p == null || e == null || e.Data == null) {
                return;
            }

            if (onResult != null) {
                onResult.Invoke(e.Data);
            }
        }

        static void p_OutputDataReceived(object sender, DataReceivedEventArgs e) {
            Process p = sender as Process;
            if (p == null || e == null || e.Data == null) {
                return;
            }

            if (onResult != null) {
                onResult.Invoke(e.Data);
            }
        }
    }
}