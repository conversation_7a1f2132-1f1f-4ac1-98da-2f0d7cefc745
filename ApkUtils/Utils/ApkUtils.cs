using System;
using System.Collections.Generic;
using System.IO;

namespace ApkUtils {
    public static class ApkUtils {
        public static string getPackageName(string pathApk) {
            string cmd = String.Format("aapt dump badging \"{0}\" | findstr -n \"package: name\" | findstr \"package\"",
                pathApk);

            List<string> results = Command.Execute(cmd);

            if(results == null || results.Count == 0) {
                return null;
            } else {
                string line1 = results[0].Split('\'')[1];
                return line1;
            }
        }

        public static void addPackageName(string path) {
            List<string> files = new List<string>();
            //Directory
            if(Directory.Exists(path)) {
                string[] strings = Directory.GetFiles(path, "*.apk");
                files.AddRange(strings);
            }

            //File
            if(File.Exists(path)) {
                files.Add(path);
            }

            if(files.Count == 0) {
                Console.WriteLine("Nothing to process in path: " + path);
            }

            foreach(string file in files) {
                if(file.Contains("[pk_")) {
                    Console.WriteLine("File: " + file + " is processed!");
                    continue;
                }

                Console.WriteLine("Process file: " + file);

                string packageName = getPackageName(file);
                FileInfo fileInfo = new FileInfo(file);
                string newFileName = fileInfo.Name.Replace(fileInfo.Extension, "") + "[pk_" + packageName + "].apk";
                newFileName = newFileName.Replace("apkpure.com", "");

                string newPathFile = Path.Combine(fileInfo.DirectoryName, newFileName);
                fileInfo.MoveTo(newPathFile);
            }
        }

        public static string InsterPackageNameAndMove(string file, string newDirectory) {
            //File
            if(!File.Exists(file)) {
                return "File not found!";
            }
            FileInfo fileInfo = new FileInfo(file);
            string newFileName = fileInfo.Name;
            if(!newFileName.Contains("[pk_")) {
                string packageName = getPackageName(file);
                newFileName = newFileName.Replace(fileInfo.Extension, "") + "[pk_" + packageName + "].apk";
                newFileName = newFileName.Replace("apkpure.com", "");
            }

            string newPathFile = Path.Combine(newDirectory, newFileName);
            fileInfo.MoveTo(newPathFile);

            return "File moved!";
        }
        public static string InsterPackageNameAndMoveXapk(string file, string newDirectory, string packageName) {
            //File
            if(!File.Exists(file)) {
                return "File not found!";
            }
            FileInfo fileInfo = new FileInfo(file);
            string newFileName = fileInfo.Name;
            if(!newFileName.Contains("[pk_")) {
                newFileName = newFileName.Replace(fileInfo.Extension, "") + "[pk_" + packageName + "].xapk";
                newFileName = newFileName.Replace("apkpure.com", "");
            }

            string newPathFile = Path.Combine(newDirectory, newFileName);
            fileInfo.MoveTo(newPathFile);

            return "File moved!";
        }
    }
}