using System;
using System.Diagnostics;
using System.IO;

namespace ApkUtils {
    class Utils {
        public static void OpenBrower(string link) {
            string operaBrower = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData),
                @"Programs\Opera\opera.exe");

            if (File.Exists(operaBrower)) {
                using (Process process = new Process()) {
                    process.StartInfo.FileName = operaBrower;
                    process.StartInfo.Arguments = link;
                    process.Start();
                }
            } else {
                MessageBox.Show("Please install Opera browser to use this feature", "Error", MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
        }

        public static string RemoveDoubleSpace(string data) {
            while (data.Contains("  ")) {
                data = data.Replace("  ", " ");
            }

            return data;
        }
    }
}