using System.IO;

namespace trungvt.Utils {
    public class FileUtils
    {
        internal static bool IsApkFile(string filePath)
        {
            string ext = GetExtFile(filePath);
            if (ext.ToLower() == "apk")
            {
                return true;
            }
            else
            {
                return false;
            }

        }

        internal static string GetExtFile(string filePath)
        {
            string fileExt = filePath.Substring(filePath.LastIndexOf(".") + 1);
            return fileExt;
        }

        internal static string GetZipPath(string filePath)
        {
            string zipPath = filePath.Substring(0, filePath.LastIndexOf(".")) + ".zip";
            return zipPath;
        }

        internal static string GetFolderPath(string filePath)
        {
            return filePath.Substring(0, filePath.LastIndexOf("."));
        }

        internal static bool IsUnityApk(string folderPath)
        {
            string lib1Path = folderPath + @"\lib\armeabi-v7a\libunity.so";
            string lib2Path = folderPath + @"\lib\x86\libunity.so";
            if (File.Exists(lib1Path) || File.Exists(lib2Path))
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        public static void PrepareExportDirectory(string path)
        {
            if (Directory.Exists(path))
            {
                Directory.Delete(path, true);
            }
        }
    }
}
