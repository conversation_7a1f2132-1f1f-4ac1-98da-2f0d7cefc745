using System.Globalization;
using System.Text;
using System.Web;
using DataAICrawler.Scripts;
using HtmlAgilityPack;

public class GameInfo {
    public enum Type {
        appbrain,
        sensortower
    }

    //private HtmlNode? product;

    public string Name { get; set; }
    public string Package { get; set; }

    public string PublishName { get; set; }

    //public string UrlIcon { get; set; }
    public long Downloads { get; set; }
    public long DailyDownloads { get; set; }

    public string? AvailableDate { get; set; }
    public string? LastUpdateDate { get; set; }

    public GameInfo() {
        AvailableDate = "01/01/1970";
        LastUpdateDate = "01/01/1970";
    }

    public GameInfo(HtmlNode? product, Type type) {
        switch (type) {
            case Type.appbrain:
                MakeFromAppBrain(product);
                break;

            case Type.sensortower:
                MakeFromSensorTower(product);
                break;
        }
    }

    private void MakeFromSensorTower(HtmlNode? htmlNode) {
        HtmlNode? childByClass = htmlNode.GetChildByClass("app-info", true).GetChildByClass("name", true);
        Name = HttpUtility.HtmlDecode(childByClass.InnerHtml);
        Package = childByClass.GetAttributeValue("href", null).Split("/")[2].Split("?")[0];
        string availableDate = htmlNode.GetChildByClass("release-date", false).InnerHtml; //MM/dd/yyyy
        if (availableDate != "-") {
            DateTime parsedDate = DateTime.Parse(availableDate, CultureInfo.InvariantCulture);
            AvailableDate = parsedDate.ToString("dd/MM/yyyy");
        }

        LastUpdateDate = Utils.GetDateAgo(htmlNode.GetChildByClass("last-update", false).InnerHtml);

        HtmlNode? app_metric = htmlNode.GetChildByClass("app-metric", false).GetChildByClass("app-metric", false);
        string innerHtml = HttpUtility.HtmlDecode(app_metric.GetChildByTag("a", true).InnerHtml).Replace("<", "");
        DailyDownloads = Utils.ConvertStringToInt(innerHtml) / 30;
    }

    private void MakeFromAppBrain(HtmlNode? htmlNode) {
        //this.product = product;

        //HtmlNode? ranking_icon = htmlNode.GetChildByClass("ranking-icon-cell");
        HtmlNode? ranking_app = htmlNode.GetChildByClass("ranking-app-cell");
        HtmlNode? tagName = ranking_app.GetChildByTag("a");

        Name = tagName.InnerHtml;
        Package = tagName.GetAttributeValue("href", null).Split("/")[3];
        PublishName = ranking_app.GetChildByClass("ranking-app-cell-creator").GetChildByTag("a").InnerHtml;
        //UrlIcon = ranking_icon.GetChildByTag("img", true).GetAttributeValue("src", null);

        string strDownloads = htmlNode.GetChildsByTag("td")[5].InnerText.Trim();
        string strRecentDownload = htmlNode.GetChildsByTag("td")[6].InnerText.Trim();
        Downloads = Utils.ConvertStringToInt(strDownloads);
        DailyDownloads = Utils.ConvertStringToInt(strRecentDownload) / 30;
    }

    public GameInfo(string? package, string? name, string? publishName, long downloads, long dailyDownloads,
                    string? availableDate, string? lastUpdateDate) {
        this.Package = package;
        this.Name = name;
        this.PublishName = publishName;
        //this.UrlIcon = urlIcon;
        this.Downloads = downloads;
        this.DailyDownloads = dailyDownloads;
        this.AvailableDate = availableDate;
        this.LastUpdateDate = lastUpdateDate;
    }

    public GameInfo(DataGridViewRow currentRow) {
        object value = currentRow.Cells["Package"].Value;
        if (value != null) {
            this.Package = value.ToString();
            this.Name = currentRow.Cells["Name"].Value.ToString();
            this.PublishName = currentRow.Cells["PublishName"].Value.ToString();
            //this.UrlIcon = currentRow.Cells["UrlIcon"].Value.ToString();
            this.Downloads = (long) currentRow.Cells["Downloads"].Value;
            this.DailyDownloads = (long) currentRow.Cells["DailyDownloads"].Value;
            this.AvailableDate = currentRow.Cells["AvailableDate"].Value.ToString();
            this.LastUpdateDate = currentRow.Cells["LastUpdateDate"].Value.ToString();
        }
    }

    public GameInfo(List<HtmlNode> htmlNodes) {
        List<HtmlNode> htmlNodes0 = htmlNodes[0].GetChildsByClass("col-12 col-sm-6");

        string innerText = htmlNodes0[0].InnerText;
        (DateTime? AvailableDate, DateTime? LastUpdateDate) value = Utils.ExtractDates(innerText);

        DateTime? availableDate = value.AvailableDate;
        DateTime? lastUpdateDate = value.LastUpdateDate;
        AvailableDate = availableDate?.ToString("dd/MM/yyyy");
        LastUpdateDate = lastUpdateDate?.ToString("dd/MM/yyyy");
        if (string.IsNullOrEmpty(AvailableDate)) {
            AvailableDate = LastUpdateDate;
        }
        //HtmlNode htmlNode0_2 = htmlNodes0[2]; //More data about
        //totalDownloads = GetDownloads(htmlNode0_2, 1);
        //int recentDownloads = GetDownloads(htmlNode0_2, 2);
        //dailyDownloads = Math.Max(recentDownloads / 30, 1);

        List<HtmlNode> childsByTag =
            htmlNodes[1].GetChildByClass("app-section").GetChildByClass("mt-2").GetChildsByTag("em");
        Downloads = Utils.ConvertStringToInt(childsByTag[0].InnerText.Trim());
        DailyDownloads = Utils.ConvertStringToInt(childsByTag[1].InnerText.Trim());

        Name = htmlNodes0[0].GetChildByTag("b").InnerText.Trim();

        HtmlNode? childByClass = htmlNodes[1].GetChildByClass("app-claim-button sexy-button sexy-button-important");
        if (childByClass != null) {
            string attributeValue = childByClass.GetAttributeValue("href", null);
            string htmlDecode = HttpUtility.HtmlDecode(attributeValue);
            Package = htmlDecode.Split("&")[1].Replace("pkg=", "");
        }

        List<HtmlNode> nodes = htmlNodes[1].GetChildsByClass("col-sm-6 app-section");
        HtmlNode? htmlNode = FindNode(nodes, "Developer information"); //Developer information
        HtmlNode? childByTag = htmlNode.GetChildByTag("a");
        PublishName = childByTag.InnerText;
    }

    private HtmlNode? FindNode(List<HtmlNode> nodes, string searchText) {
        foreach (HtmlNode node in nodes) {
            if (node.InnerText.Contains(searchText)) {
                return node;
            }
        }

        return null;
    }

    private int GetDownloads(HtmlNode htmlNode, int index) {
        List<HtmlNode> tagTr = htmlNode.GetChildsByTag("tr", true);
        string innerText = tagTr[index].GetChildsByTag("td")[1].InnerText;

        int downloads = Utils.ConvertStringToInt(innerText);
        return downloads;
    }

    public string GetResult() {
        StringBuilder sb = new();
        sb.AppendLine($"Package: {Package}");
        sb.AppendLine($"Name: {Name}");
        sb.AppendLine($"PublishName: {PublishName}");
        sb.AppendLine($"AvailableDate: {AvailableDate}");
        sb.AppendLine($"LastUpdateDate: {LastUpdateDate}");
        sb.AppendLine($"TotalDownloads: {Downloads}");
        sb.AppendLine($"DailyDownloads: {DailyDownloads}");
        return sb.ToString();
    }
}