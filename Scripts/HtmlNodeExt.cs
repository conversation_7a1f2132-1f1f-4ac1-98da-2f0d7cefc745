using HtmlAgilityPack;
using System;

public static class HtmlNodeExt {
    public static HtmlNode? GetChildByClass(this HtmlNode? node, string className, bool isFindChild = true) {
        if (node == null) {
            return null;
        }

        foreach (HtmlNode? child in node.ChildNodes) {
            if (child.GetAttributeValue("class", "") == className) {
                return child;
            }

            if (isFindChild && child.ChildNodes.Count > 0) {
                HtmlNode? htmlNode = child.GetChildByClass(className, isFindChild);
                if (htmlNode != null) {
                    return htmlNode;
                }
            }
        }

        return null;
    }

    public static List<HtmlNode> GetChildsByClass(this HtmlNode? node, string className, bool isFindChild = false) {
        if (node == null) {
            return null;
        }

        List<HtmlNode> results = new List<HtmlNode>();

        foreach (HtmlNode? child in node.ChildNodes) {
            if (child.GetAttributeValue("class", "") == className) {
                results.Add(child);
            }

            if (isFindChild && child.ChildNodes.Count > 0) {
                List<HtmlNode> htmlNode = child.GetChildsByClass(className, isFindChild);
                if (htmlNode != null) {
                    results.AddRange(htmlNode);
                }
            }
        }

        return results;
    }

    public static HtmlNode? GetChildByTag(this HtmlNode? node, string tagName, bool isFindChild = true) {
        if (node == null) {
            return null;
        }

        foreach (HtmlNode? child in node.ChildNodes) {
            if (child.Name == tagName) {
                return child;
            }

            if (isFindChild && child.ChildNodes.Count > 0) {
                HtmlNode? htmlNode = child.GetChildByTag(tagName, isFindChild);
                if (htmlNode != null) {
                    return htmlNode;
                }
            }
        }

        return null;
    }

    public static List<HtmlNode> GetChildsByTag(this HtmlNode? node, string tagName, bool isFindChild = false) {
        if (node == null) {
            return null;
        }

        List<HtmlNode> results = new List<HtmlNode>();

        foreach (HtmlNode? child in node.ChildNodes) {
            if (child.Name == tagName) {
                results.Add(child);
            }

            if (isFindChild && child.ChildNodes.Count > 0) {
                List<HtmlNode> htmlNode = child.GetChildsByTag(tagName, isFindChild);
                if (htmlNode != null) {                    
                    results.AddRange(htmlNode);
                }
            }
        }

        return results;
    }
}
