using System.Security.Cryptography;
using System.Text.RegularExpressions;

namespace DataAICrawler.Scripts {
    public static class Utils {
        static Dictionary<string, int> strToInt = new() {
            { "thousand", 1000 },
            { "k", 1000 },
            { "million", 1000000 },
            { "m", 1000000 },
            { "billion", 1000000000 },
            { "b", 1000000000 }
        };

        public static int ConvertStringToInt(string input) {
            if (string.IsNullOrWhiteSpace(input)) {
                throw new ArgumentException("Input cannot be null or empty.");
            }

            input = input.ToLower().Trim(); // Chuyển đổi về chữ thường và loại bỏ khoảng trắng thừa.

            foreach (KeyValuePair<string, int> pair in strToInt) {
                if (input.Contains(pair.Key)) {
                    string parts = input.Replace(pair.Key, "").Trim();
                    if (!float.TryParse(parts, out float number)) {
                        throw new FormatException("The first part of the input must be a valid number.");
                    }

                    return (int) (number * pair.Value);
                }
            }

            if (int.TryParse(input, out int result)) {
                return result;
            } else {
                return -1;
            }
        }

        // Regex patterns for matching dates
        static string availablePattern  = @"The APK has been available since (?<date>[A-Za-z]+ \d{4})\.";
        static string pattern           = @"The APK has been available since (\d+)\s+weeks ago";
        static string lastUpdatePattern = @"The last update of the app was on (?<date>[A-Za-z]+ \d{1,2}, \d{4})\.";

        public static (DateTime? AvailableDate, DateTime? LastUpdateDate) ExtractDates(string input) {
            if (string.IsNullOrWhiteSpace(input)) {
                throw new ArgumentException("Input cannot be null or empty.");
            }

            DateTime? availableDate = null;
            DateTime? lastUpdateDate = null;

            // Match for available date
            Match availableMatch = Regex.Match(input, availablePattern);
            if (availableMatch.Success) {
                if (DateTime.TryParse(availableMatch.Groups["date"].Value, out DateTime parsedAvailableDate)) {
                    availableDate = parsedAvailableDate;
                }
            } else {
                Match match = Regex.Match(input, pattern);
                if (match.Success) {
                    int weeksAgo = int.Parse(match.Groups[1].Value); // Lấy số tuần
                    availableDate = DateTime.Now.AddDays(-weeksAgo * 7); // Trừ số tuần ra ngày tương ứng
                }
            }

            // Match for last update date
            Match lastUpdateMatch = Regex.Match(input, lastUpdatePattern);
            if (lastUpdateMatch.Success &&
                DateTime.TryParse(lastUpdateMatch.Groups["date"].Value, out DateTime parsedLastUpdateDate)) {
                lastUpdateDate = parsedLastUpdateDate;
            }

            return (availableDate, lastUpdateDate);
        }

        private static Dictionary<string, int> strToDate = new() {
            { "days", 1 },
            { "weeks", 7 },
            { "months", 30 },
            { "years", 365 }
        };

        public static string? GetDateAgo(string input) {
            if (string.IsNullOrWhiteSpace(input) || input == "-") {
                return null;
            }

            foreach (KeyValuePair<string, int> pair in strToDate) {
                if (!input.Contains(pair.Key)) {
                    continue;
                }

                string parts = input.Replace(pair.Key, "").Trim();
                if (!float.TryParse(parts, out float number)) {
                    throw new FormatException("The first part of the input must be a valid number.");
                }

                int day = (int) (number * pair.Value);
                return DateTime.Now.AddDays(-day).ToString("dd/MM/yyyy");
            }

            return null;
        }
    }
}