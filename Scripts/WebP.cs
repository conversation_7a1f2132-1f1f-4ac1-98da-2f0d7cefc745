using System.Runtime.InteropServices;
using Microsoft.VisualBasic.Logging;

namespace DataAICrawler;

public class WebP {
    // Import hàm giải mã từ libwebp.dll
    [DllImport("libwebp.dll", CallingConvention = CallingConvention.Cdecl)]
    public static extern IntPtr WebPDecodeBGRA(IntPtr data, UInt32 dataSize, ref int width, ref int height);

    // Import hàm để giải phóng bộ nhớ sau khi sử dụng
    [DllImport("libwebp.dll", CallingConvention = CallingConvention.Cdecl)]
    public static extern void WebPFree(IntPtr p);

    // Giải mã ảnh WebP thành Bitmap
    public static Bitmap LoadWebP(byte[] rawWebP) {
        int width = 0, height = 0;

        // Gán dữ liệu WebP vào một con trỏ
        GCHandle pinnedArray = GCHandle.Alloc(rawWebP, GCHandleType.Pinned);
        IntPtr ptrData = pinnedArray.AddrOfPinnedObject();

        try {
            // G<PERSON><PERSON>i mã hình ảnh WebP
            IntPtr outputBuffer = WebPDecodeBGRA(ptrData, (UInt32) rawWebP.Length, ref width, ref height);
            if (outputBuffer == IntPtr.Zero)
                throw new Exception("Failed to decode WebP image.");

            // Tạo Bitmap từ buffer
            Bitmap bmp = new Bitmap(width, height, System.Drawing.Imaging.PixelFormat.Format32bppArgb);
            var bmpData = bmp.LockBits(new Rectangle(0, 0, width, height),
                System.Drawing.Imaging.ImageLockMode.WriteOnly, bmp.PixelFormat);

            // Tạo một mảng byte để chứa dữ liệu BGRA
            byte[] rawImageData = new byte[width * height * 4]; // 4 bytes cho mỗi pixel (BGRA)

            // Sao chép dữ liệu từ buffer WebP vào mảng byte
            Marshal.Copy(outputBuffer, rawImageData, 0, rawImageData.Length);

            // Sao chép dữ liệu từ mảng byte vào vùng nhớ của Bitmap
            Marshal.Copy(rawImageData, 0, bmpData.Scan0, rawImageData.Length);

            bmp.UnlockBits(bmpData);

            // Giải phóng bộ nhớ sau khi sử dụng
            WebPFree(outputBuffer);

            return bmp;
        } catch (Exception ex) {
            Console.WriteLine(ex.Message);
            return null;
        } finally {
            // Giải phóng con trỏ pinnedArray
            pinnedArray.Free();
        }
    }
}